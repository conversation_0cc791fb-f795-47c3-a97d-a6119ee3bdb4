"""
问题1可视化代码：生成烟幕弹投放策略的动态演示图
包含：3D轨迹图、遮蔽时间分析图、关键参数变化图
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.patches as patches
from matplotlib.animation import FuncAnimation
import seaborn as sns

# 设置中文字体和图表样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")

# 导入问题1的核心函数
from problem1 import *

def create_problem1_visualization():
    """创建问题1的综合可视化图表"""
    
    # 创建图形布局
    fig = plt.figure(figsize=(20, 12))
    fig.suptitle('烟幕弹投放策略数学建模分析 - 问题1可视化', fontsize=16, fontweight='bold')
    
    # 1. 3D轨迹图
    ax1 = fig.add_subplot(2, 3, 1, projection='3d')
    plot_3d_trajectories(ax1)
    
    # 2. 遮蔽效果时间序列
    ax2 = fig.add_subplot(2, 3, 2)
    plot_blocking_timeline(ax2)
    
    # 3. 关键几何参数变化
    ax3 = fig.add_subplot(2, 3, 3)
    plot_geometric_parameters(ax3)
    
    # 4. 视锥半角变化
    ax4 = fig.add_subplot(2, 3, 4)
    plot_cone_angle_evolution(ax4)
    
    # 5. 烟幕球与视锥相对位置
    ax5 = fig.add_subplot(2, 3, 5)
    plot_relative_geometry(ax5)
    
    # 6. 遮蔽效果统计分析
    ax6 = fig.add_subplot(2, 3, 6)
    plot_blocking_statistics(ax6)
    
    plt.tight_layout()
    return fig

def plot_3d_trajectories(ax):
    """绘制3D轨迹图"""
    # 时间范围
    t_range = np.linspace(0, 30, 300)
    
    # 计算各实体轨迹
    missile_traj = np.array([P_M(t) for t in t_range])
    uav_traj = np.array([P_U(t) for t in t_range if t <= 15])  # UAV飞行15秒
    
    # 干扰弹轨迹（投放后）
    grenade_times = t_range[t_range >= t_drop]
    grenade_traj = np.array([P_G(t) for t in grenade_times])
    
    # 烟幕球轨迹（起爆后）
    smoke_times = t_range[t_range >= t_det]
    smoke_traj = np.array([P_C(t) for t in smoke_times])
    
    # 绘制轨迹
    ax.plot(missile_traj[:, 0], missile_traj[:, 1], missile_traj[:, 2], 
            'r-', linewidth=3, label='导弹M1轨迹', alpha=0.8)
    ax.plot(uav_traj[:, 0], uav_traj[:, 1], uav_traj[:, 2], 
            'b-', linewidth=2, label='无人机FY1轨迹', alpha=0.8)
    ax.plot(grenade_traj[:, 0], grenade_traj[:, 1], grenade_traj[:, 2], 
            'g--', linewidth=2, label='干扰弹轨迹', alpha=0.7)
    ax.plot(smoke_traj[:, 0], smoke_traj[:, 1], smoke_traj[:, 2], 
            'orange', linewidth=3, label='烟幕球轨迹', alpha=0.9)
    
    # 标记关键点
    ax.scatter(*P_M0, color='red', s=100, marker='^', label='M1初始位置')
    ax.scatter(*P_U0, color='blue', s=100, marker='s', label='FY1初始位置')
    ax.scatter(*P_T_center, color='purple', s=150, marker='*', label='保护目标')
    
    # 绘制目标圆柱体
    draw_cylinder(ax, P_T_bottom, R_T, H_T)
    
    ax.set_xlabel('X (米)')
    ax.set_ylabel('Y (米)')
    ax.set_zlabel('Z (米)')
    ax.set_title('三维空间轨迹图')
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

def plot_blocking_timeline(ax):
    """绘制遮蔽效果时间序列"""
    # 时间范围（烟幕有效期）
    t_range = np.linspace(t_det, t_det + T_eff, 2000)
    blocking_status = [is_blocked(t) for t in t_range]
    
    # 绘制遮蔽状态
    ax.fill_between(t_range, 0, blocking_status, alpha=0.7, color='green', 
                    label='有效遮蔽时段')
    ax.plot(t_range, blocking_status, 'k-', linewidth=1, alpha=0.8)
    
    # 计算并标注总遮蔽时间
    total_time = sum(blocking_status) * (t_range[1] - t_range[0])
    ax.text(0.7, 0.8, f'总遮蔽时间: {total_time:.2f}秒', 
            transform=ax.transAxes, fontsize=12, 
            bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
    
    ax.set_xlabel('时间 (秒)')
    ax.set_ylabel('遮蔽状态')
    ax.set_title('遮蔽效果时间序列')
    ax.set_ylim(-0.1, 1.1)
    ax.legend()

def plot_geometric_parameters(ax):
    """绘制关键几何参数变化"""
    t_range = np.linspace(t_det, t_det + T_eff, 200)
    
    d_axis_values = []
    r_cone_values = []
    
    for t in t_range:
        if t >= t_det and t <= t_det + T_eff:
            V = P_M(t)
            A = view_cone_axis(t)
            theta = view_cone_half_angle(t)
            C = P_C(t)
            
            L = sub(C, V)
            d_along = dot(L, A)
            if d_along > 0:
                L2 = dot(L, L)
                perp2 = max(L2 - d_along * d_along, 0.0)
                d_axis = sqrt(perp2)
                R_cone = d_along * tan(theta)
            else:
                d_axis = float('inf')
                R_cone = 0
        else:
            d_axis = float('inf')
            R_cone = 0
            
        d_axis_values.append(d_axis if d_axis != float('inf') else np.nan)
        r_cone_values.append(R_cone)
    
    ax.plot(t_range, d_axis_values, 'b-', linewidth=2, label='球心到轴线距离 $d_{axis}$')
    ax.plot(t_range, r_cone_values, 'r-', linewidth=2, label='视锥半径 $R_{cone}$')
    ax.axhline(y=R_C, color='green', linestyle='--', linewidth=2, label='烟幕球半径 $R_C$')
    
    ax.set_xlabel('时间 (秒)')
    ax.set_ylabel('距离 (米)')
    ax.set_title('关键几何参数变化')
    ax.legend()
    ax.grid(True, alpha=0.3)

def plot_cone_angle_evolution(ax):
    """绘制视锥半角变化"""
    t_range = np.linspace(0, 25, 250)
    cone_angles = [view_cone_half_angle(t) * 180 / np.pi for t in t_range]
    
    ax.plot(t_range, cone_angles, 'purple', linewidth=2, label='视锥半角')
    ax.axvline(x=t_det, color='red', linestyle='--', alpha=0.7, label='烟幕起爆时刻')
    ax.axvline(x=t_det + T_eff, color='red', linestyle='--', alpha=0.7, label='烟幕失效时刻')
    
    ax.set_xlabel('时间 (秒)')
    ax.set_ylabel('角度 (度)')
    ax.set_title('视锥半角随时间变化')
    ax.legend()
    ax.grid(True, alpha=0.3)

def plot_relative_geometry(ax):
    """绘制烟幕球与视锥相对位置示意图"""
    # 选择一个典型时刻进行分析
    t_sample = t_det + 5
    
    V = P_M(t_sample)
    C = P_C(t_sample)
    A = view_cone_axis(t_sample)
    theta = view_cone_half_angle(t_sample)
    
    # 投影到2D平面进行可视化
    # 简化为XZ平面投影
    ax.scatter(V[0], V[2], color='red', s=100, marker='^', label='导弹位置')
    ax.scatter(C[0], C[2], color='orange', s=200, marker='o', label='烟幕球心')
    ax.scatter(P_T_center[0], P_T_center[2], color='purple', s=150, marker='*', label='目标中心')
    
    # 绘制视锥轮廓（简化）
    cone_length = 1000
    cone_width = cone_length * np.tan(theta)
    
    cone_x = [V[0], V[0] + cone_length, V[0] + cone_length, V[0]]
    cone_z = [V[2], V[2] + cone_width, V[2] - cone_width, V[2]]
    ax.plot(cone_x, cone_z, 'r--', alpha=0.5, label='视锥边界')
    
    # 绘制烟幕球
    circle = plt.Circle((C[0], C[2]), R_C, fill=False, color='orange', linewidth=2)
    ax.add_patch(circle)
    
    ax.set_xlabel('X (米)')
    ax.set_ylabel('Z (米)')
    ax.set_title(f'几何关系示意图 (t={t_sample:.1f}s)')
    ax.legend()
    ax.axis('equal')
    ax.grid(True, alpha=0.3)

def plot_blocking_statistics(ax):
    """绘制遮蔽效果统计分析"""
    # 分析不同时间段的遮蔽效果
    time_segments = np.linspace(t_det, t_det + T_eff, 21)
    segment_blocking = []
    
    for i in range(len(time_segments) - 1):
        t_start, t_end = time_segments[i], time_segments[i+1]
        t_seg = np.linspace(t_start, t_end, 100)
        blocking_ratio = sum(is_blocked(t) for t in t_seg) / len(t_seg)
        segment_blocking.append(blocking_ratio)
    
    segment_centers = (time_segments[:-1] + time_segments[1:]) / 2
    
    bars = ax.bar(segment_centers - t_det, segment_blocking, 
                  width=0.8, alpha=0.7, color='skyblue', edgecolor='navy')
    
    # 添加数值标签
    for bar, ratio in zip(bars, segment_blocking):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{ratio:.2f}', ha='center', va='bottom', fontsize=8)
    
    ax.set_xlabel('相对时间 (秒)')
    ax.set_ylabel('遮蔽效果比例')
    ax.set_title('分时段遮蔽效果统计')
    ax.set_ylim(0, 1.1)
    ax.grid(True, alpha=0.3)

def draw_cylinder(ax, center, radius, height):
    """在3D图中绘制圆柱体"""
    # 底面圆
    theta = np.linspace(0, 2*np.pi, 50)
    x_circle = center[0] + radius * np.cos(theta)
    y_circle = center[1] + radius * np.sin(theta)
    z_bottom = np.full_like(x_circle, center[2])
    z_top = np.full_like(x_circle, center[2] + height)
    
    ax.plot(x_circle, y_circle, z_bottom, 'purple', alpha=0.6)
    ax.plot(x_circle, y_circle, z_top, 'purple', alpha=0.6)
    
    # 侧面线条
    for i in range(0, len(theta), 10):
        ax.plot([x_circle[i], x_circle[i]], 
                [y_circle[i], y_circle[i]], 
                [z_bottom[i], z_top[i]], 'purple', alpha=0.4)

def create_summary_report():
    """生成问题1的数值分析报告"""
    print("="*60)
    print("问题1：烟幕弹投放策略数值分析报告")
    print("="*60)

    # 计算关键数值
    P_d, P_det = sanity_check_points()
    total_time = compute_effective遮蔽时长(0.01)

    print(f"1. 关键位置信息：")
    print(f"   投放点位置: ({P_d[0]:.1f}, {P_d[1]:.1f}, {P_d[2]:.1f}) 米")
    print(f"   起爆点位置: ({P_det[0]:.1f}, {P_det[1]:.1f}, {P_det[2]:.1f}) 米")

    print(f"\n2. 时间参数：")
    print(f"   投放时间: {t_drop:.1f} 秒")
    print(f"   起爆时间: {t_det:.1f} 秒")
    print(f"   有效窗口: {t_det:.1f} - {t_det + T_eff:.1f} 秒")

    print(f"\n3. 遮蔽效果分析：")
    print(f"   总有效遮蔽时长: {total_time:.3f} 秒")
    print(f"   遮蔽效率: {total_time/T_eff*100:.1f}%")

    # 分析关键时刻的几何参数
    critical_times = [t_det + 2, t_det + 5, t_det + 10, t_det + 15]
    print(f"\n4. 关键时刻几何参数分析：")
    for t in critical_times:
        if t <= t_det + T_eff:
            V = P_M(t)
            C = P_C(t)
            A = view_cone_axis(t)
            theta = view_cone_half_angle(t)

            L = sub(C, V)
            d_along = dot(L, A)
            if d_along > 0:
                L2 = dot(L, L)
                perp2 = max(L2 - d_along * d_along, 0.0)
                d_axis = sqrt(perp2)
                R_cone = d_along * tan(theta)
                blocked = is_blocked(t)

                print(f"   t={t:.1f}s: d_axis={d_axis:.2f}m, R_cone={R_cone:.2f}m, "
                      f"遮蔽={blocked}")

    print("="*60)

if __name__ == "__main__":
    # 生成数值分析报告
    create_summary_report()

    # 生成可视化图表
    print("\n正在生成可视化图表...")
    fig = create_problem1_visualization()

    # 保存图片
    plt.savefig('problem1_analysis.png', dpi=300, bbox_inches='tight')
    plt.savefig('problem1_analysis.pdf', bbox_inches='tight')

    print("\n问题1可视化图表已生成：")
    print("- problem1_analysis.png (高分辨率PNG)")
    print("- problem1_analysis.pdf (矢量PDF)")

    plt.show()
