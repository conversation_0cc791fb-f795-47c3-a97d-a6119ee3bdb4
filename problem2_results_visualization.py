"""
问题2优化结果可视化
展示PSO收敛过程、最优策略参数和性能对比
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.patches as patches
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
import matplotlib
matplotlib.rcParams['font.family'] = ['SimHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 12
plt.rcParams['axes.linewidth'] = 1.2
plt.rcParams['lines.linewidth'] = 2.0

# 美观配色方案
COLORS = {
    'primary': '#2E86AB',      # 蓝色
    'secondary': '#A23B72',    # 紫红色
    'accent': '#F18F01',       # 橙色
    'success': '#C73E1D',      # 红色
    'background': '#F5F5F5'    # 浅灰色
}

def create_problem2_analysis():
    """创建问题2分析图表"""
    
    fig = plt.figure(figsize=(16, 12), facecolor='white')
    fig.suptitle('问题2：烟幕干扰弹最优投放策略分析', fontsize=16, fontweight='bold', y=0.95)
    
    # 2x2布局
    ax1 = plt.subplot(2, 2, 1)
    plot_pso_convergence(ax1)
    
    ax2 = plt.subplot(2, 2, 2)
    plot_strategy_comparison(ax2)
    
    ax3 = plt.subplot(2, 2, 3, projection='3d')
    plot_optimal_trajectory(ax3)
    
    ax4 = plt.subplot(2, 2, 4)
    plot_parameter_analysis(ax4)
    
    plt.tight_layout(rect=[0, 0.03, 1, 0.93])
    return fig

def plot_pso_convergence(ax):
    """绘制PSO收敛过程"""
    # 基于实际运行结果的收敛数据
    iterations = list(range(1, 201))
    fitness_values = []
    
    # 构建收敛曲线数据
    for i in iterations:
        if i <= 15:
            fitness_values.append(0.0)
        elif i == 16:
            fitness_values.append(2.5)
        elif i <= 17:
            fitness_values.append(2.5)
        elif i == 18:
            fitness_values.append(3.44)
        elif i <= 19:
            fitness_values.append(3.44)
        elif i <= 68:
            fitness_values.append(4.46)
        elif i <= 84:
            fitness_values.append(4.48)
        elif i <= 112:
            fitness_values.append(4.49)
        elif i <= 133:
            fitness_values.append(4.50)
        else:
            fitness_values.append(4.51)
    
    # 绘制收敛曲线
    ax.plot(iterations, fitness_values, color=COLORS['primary'], linewidth=2.5, alpha=0.8)
    ax.fill_between(iterations, 0, fitness_values, alpha=0.3, color=COLORS['primary'])
    
    # 标注关键点
    key_points = [(16, 2.5, '首次发现可行解'), (20, 4.46, '快速提升阶段'), 
                  (134, 4.51, '达到最优解')]
    
    for iter_num, value, label in key_points:
        ax.annotate(f'{label}\n第{iter_num}代: {value}s', 
                   xy=(iter_num, value), xytext=(iter_num+20, value+0.3),
                   arrowprops=dict(arrowstyle='->', color='red', lw=1.5),
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='yellow', alpha=0.7),
                   fontsize=10, ha='center')
    
    ax.set_xlabel('迭代次数', fontsize=12, weight='bold')
    ax.set_ylabel('有效遮蔽时长 (秒)', fontsize=12, weight='bold')
    ax.set_title('PSO算法收敛过程', fontsize=13, weight='bold', pad=15)
    ax.grid(True, alpha=0.3)
    ax.set_xlim(0, 200)
    ax.set_ylim(0, 5)

def plot_strategy_comparison(ax):
    """绘制策略对比"""
    strategies = ['问题1\n固定策略', '问题2\n最优策略']
    performance = [1.34, 4.51]
    colors = [COLORS['secondary'], COLORS['success']]
    
    bars = ax.bar(strategies, performance, color=colors, alpha=0.8, 
                  edgecolor='black', linewidth=1.5)
    
    # 添加数值标签
    for bar, value in zip(bars, performance):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{value:.2f}秒', ha='center', va='bottom', 
                fontsize=12, weight='bold')
    
    # 添加提升率标注
    improvement = (4.51 - 1.34) / 1.34 * 100
    ax.annotate(f'性能提升\n{improvement:.1f}%', 
               xy=(1, 4.51), xytext=(1.3, 3.5),
               arrowprops=dict(arrowstyle='->', color='green', lw=2),
               bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgreen', alpha=0.8),
               fontsize=11, weight='bold', ha='center')
    
    ax.set_ylabel('有效遮蔽时长 (秒)', fontsize=12, weight='bold')
    ax.set_title('策略性能对比', fontsize=13, weight='bold', pad=15)
    ax.set_ylim(0, 5)
    ax.grid(True, alpha=0.3, axis='y')

def plot_optimal_trajectory(ax):
    """绘制最优策略3D轨迹"""
    # 最优策略参数
    v_u_opt = 70.236
    theta_opt = 0.069829  # 4.00度
    t_drop_opt = 1.728
    t_delay_opt = 0.000
    
    # 初始位置
    P_M0 = np.array([20000, 0, 2000])
    P_U0 = np.array([17800, 0, 1800])
    P_T_center = np.array([0, 200, 5])
    
    # 时间范围
    t_range = np.linspace(0, 10, 100)
    
    # 导弹轨迹
    v_M = 300
    M_dir = -P_M0 / np.linalg.norm(P_M0)
    missile_traj = np.array([P_M0 + v_M * t * M_dir for t in t_range])
    
    # 无人机轨迹（最优策略）
    U_dir_opt = np.array([np.cos(theta_opt), np.sin(theta_opt), 0])
    uav_traj = np.array([P_U0 + v_u_opt * t * U_dir_opt for t in t_range if t <= 5])
    
    # 投放点和起爆点
    P_drop_opt = P_U0 + v_u_opt * t_drop_opt * U_dir_opt
    P_det_opt = P_drop_opt  # 延迟为0，重合
    
    # 绘制轨迹
    ax.plot(missile_traj[:, 0], missile_traj[:, 1], missile_traj[:, 2], 
            color=COLORS['success'], linewidth=3, label='导弹M1轨迹', alpha=0.8)
    ax.plot(uav_traj[:, 0], uav_traj[:, 1], uav_traj[:, 2], 
            color=COLORS['primary'], linewidth=3, label='无人机FY1最优轨迹', alpha=0.8)
    
    # 标记关键点
    ax.scatter(*P_M0, color=COLORS['success'], s=120, marker='^', 
              label='M1起点', edgecolors='white', linewidth=2, zorder=5)
    ax.scatter(*P_U0, color=COLORS['primary'], s=120, marker='s', 
              label='FY1起点', edgecolors='white', linewidth=2, zorder=5)
    ax.scatter(*P_T_center, color=COLORS['accent'], s=180, marker='*', 
              label='保护目标', edgecolors='white', linewidth=2, zorder=5)
    ax.scatter(*P_drop_opt, color='orange', s=150, marker='o', 
              label='最优投放/起爆点', edgecolors='white', linewidth=2, zorder=5)
    
    # 绘制目标圆柱体
    theta_cyl = np.linspace(0, 2*np.pi, 20)
    R_T = 7
    H_T = 10
    P_T_bottom = np.array([0, 200, 0])
    
    x_cyl = P_T_bottom[0] + R_T * np.cos(theta_cyl)
    y_cyl = P_T_bottom[1] + R_T * np.sin(theta_cyl)
    z_bottom = np.full_like(x_cyl, P_T_bottom[2])
    z_top = np.full_like(x_cyl, P_T_bottom[2] + H_T)
    
    ax.plot(x_cyl, y_cyl, z_bottom, color=COLORS['accent'], alpha=0.8, linewidth=2)
    ax.plot(x_cyl, y_cyl, z_top, color=COLORS['accent'], alpha=0.8, linewidth=2)
    
    # 添加参数标注
    ax.text2D(0.02, 0.98, f'最优参数:\nv_U = {v_u_opt:.1f} m/s\nθ = {theta_opt*180/np.pi:.1f}°\nt_drop = {t_drop_opt:.2f}s', 
              transform=ax.transAxes, fontsize=10, verticalalignment='top',
              bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
    
    ax.set_xlabel('X (m)', fontsize=12, weight='bold')
    ax.set_ylabel('Y (m)', fontsize=12, weight='bold')
    ax.set_zlabel('Z (m)', fontsize=12, weight='bold')
    ax.set_title('最优策略三维轨迹', fontsize=13, weight='bold', pad=15)
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=9)
    ax.view_init(elev=20, azim=45)

def plot_parameter_analysis(ax):
    """绘制参数分析"""
    # 最优参数
    params = ['飞行速度\n(m/s)', '飞行方向\n(度)', '投放时间\n(秒)', '引爆延迟\n(秒)']
    optimal_values = [70.236, 4.00, 1.728, 0.000]
    problem1_values = [120.0, 180.0, 1.5, 3.6]  # 问题1对应值（方向为朝向原点）
    
    x = np.arange(len(params))
    width = 0.35
    
    bars1 = ax.bar(x - width/2, problem1_values, width, label='问题1固定策略', 
                   color=COLORS['secondary'], alpha=0.7)
    bars2 = ax.bar(x + width/2, optimal_values, width, label='问题2最优策略', 
                   color=COLORS['success'], alpha=0.7)
    
    # 添加数值标签
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + max(problem1_values)*0.01,
                    f'{height:.2f}', ha='center', va='bottom', fontsize=10)
    
    ax.set_xlabel('决策变量', fontsize=12, weight='bold')
    ax.set_ylabel('参数值', fontsize=12, weight='bold')
    ax.set_title('决策变量对比分析', fontsize=13, weight='bold', pad=15)
    ax.set_xticks(x)
    ax.set_xticklabels(params)
    ax.legend()
    ax.grid(True, alpha=0.3, axis='y')

def create_summary_table():
    """创建结果汇总表"""
    fig, ax = plt.subplots(figsize=(12, 8), facecolor='white')
    ax.axis('tight')
    ax.axis('off')
    
    # 表格数据
    data = [
        ['参数类别', '参数名称', '问题1固定值', '问题2最优值', '变化说明'],
        ['飞行参数', '速度 v_U (m/s)', '120.0', '70.236', '降低41.5%，提高精度'],
        ['', '方向角 θ (度)', '180.0', '4.0', '几乎沿x轴负方向'],
        ['时间参数', '投放时间 t_drop (s)', '1.5', '1.728', '延后0.228秒'],
        ['', '引爆延迟 t_delay (s)', '3.6', '0.0', '立即起爆策略'],
        ['位置参数', '投放点 x坐标 (m)', '17620.0', '17921.1', '更接近目标'],
        ['', '投放点 y坐标 (m)', '0.0', '8.5', '轻微偏移'],
        ['', '投放点 z坐标 (m)', '1800.0', '1800.0', '保持高度'],
        ['性能指标', '有效遮蔽时长 (s)', '1.34', '4.51', '提升236.6%'],
        ['', '遮蔽效率 (%)', '6.7', '22.6', '显著提升']
    ]
    
    # 创建表格
    table = ax.table(cellText=data[1:], colLabels=data[0], cellLoc='center', loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(11)
    table.scale(1.2, 2)
    
    # 设置表格样式
    for i in range(len(data)):
        for j in range(len(data[0])):
            cell = table[(i, j)]
            if i == 0:  # 表头
                cell.set_facecolor('#4472C4')
                cell.set_text_props(weight='bold', color='white')
            elif j == 0:  # 第一列
                cell.set_facecolor('#D9E2F3')
                cell.set_text_props(weight='bold')
            elif j == 4:  # 最后一列
                cell.set_facecolor('#E2EFDA')
            else:
                cell.set_facecolor('#F2F2F2')
    
    plt.title('问题2最优策略详细对比分析', fontsize=16, weight='bold', pad=20)
    return fig

if __name__ == "__main__":
    print("正在生成问题2分析图表...")
    
    # 生成主要分析图
    fig1 = create_problem2_analysis()
    fig1.savefig('problem2_analysis.png', dpi=300, bbox_inches='tight', facecolor='white')
    fig1.savefig('problem2_analysis.pdf', bbox_inches='tight', facecolor='white')
    
    # 生成汇总表
    fig2 = create_summary_table()
    fig2.savefig('problem2_summary_table.png', dpi=300, bbox_inches='tight', facecolor='white')
    fig2.savefig('problem2_summary_table.pdf', bbox_inches='tight', facecolor='white')
    
    print("\n问题2分析图表已生成：")
    print("1. problem2_analysis.png/pdf - 综合分析图")
    print("2. problem2_summary_table.png/pdf - 结果汇总表")
    
    print("\n关键结果总结：")
    print("="*50)
    print("最优策略参数：")
    print(f"  飞行速度: 70.236 m/s")
    print(f"  飞行方向: 4.00° (几乎沿x轴负方向)")
    print(f"  投放时间: 1.728 s")
    print(f"  引爆延迟: 0.000 s (立即起爆)")
    print(f"  投放/起爆点: (17921.1, 8.5, 1800.0) m")
    print(f"  最大遮蔽时长: 4.51 s")
    print(f"  性能提升: 236.6%")
    print("="*50)
    
    plt.show()
