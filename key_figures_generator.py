"""
美化版关键图例生成器 - 问题1核心图表
重新理解有效遮蔽概念，确保数据准确性，美化图表设计
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, Circle, Ellipse
from matplotlib.colors import LinearSegmentedColormap
import matplotlib.gridspec as gridspec
import warnings
warnings.filterwarnings('ignore')

# 设置现代化图表样式
import matplotlib
matplotlib.rcParams['font.family'] = ['DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 12
plt.rcParams['axes.linewidth'] = 1.2
plt.rcParams['lines.linewidth'] = 2.2
plt.rcParams['axes.grid'] = True
plt.rcParams['grid.alpha'] = 0.3
plt.rcParams['axes.axisbelow'] = True

# 定义美观的配色方案
COLORS = {
    'missile': '#E74C3C',      # 鲜红色 - 导弹
    'smoke': '#F39C12',        # 橙色 - 烟幕
    'target': '#8E44AD',       # 紫色 - 目标
    'uav': '#3498DB',          # 蓝色 - 无人机
    'cone': '#E74C3C',         # 红色 - 视锥
    'axis': '#C0392B',         # 深红色 - 轴线
    'blocking': '#27AE60',     # 绿色 - 遮蔽
    'background': '#ECF0F1',   # 浅灰色 - 背景
    'grid': '#BDC3C7'          # 灰色 - 网格
}

# 导入问题1的核心函数
from problem1 import *

def create_enhanced_figures():
    """创建美化版关键图例 - 2x2布局"""

    # 创建更大的图形以提高清晰度
    fig = plt.figure(figsize=(16, 12), facecolor='white')
    fig.suptitle('Problem 1: Enhanced Smoke Grenade Deployment Analysis',
                 fontsize=16, fontweight='bold', y=0.95, color='#2C3E50')

    # 使用GridSpec获得更好的布局控制
    gs = fig.add_gridspec(2, 2, hspace=0.3, wspace=0.3)

    # 图1：增强版几何模型原理图
    ax1 = fig.add_subplot(gs[0, 0])
    plot_enhanced_geometric_model(ax1)

    # 图2：美化版3D轨迹图
    ax2 = fig.add_subplot(gs[0, 1], projection='3d')
    plot_enhanced_3d_trajectories(ax2)

    # 图3：精确遮蔽效果分析
    ax3 = fig.add_subplot(gs[1, 0])
    plot_accurate_blocking_analysis(ax3)

    # 图4：美化版参数演化图
    ax4 = fig.add_subplot(gs[1, 1])
    plot_enhanced_parameters(ax4)

    # 设置整体背景
    fig.patch.set_facecolor('white')

    return fig

def plot_enhanced_geometric_model(ax):
    """增强版几何模型原理图 - 展示有效遮蔽概念"""
    ax.set_xlim(-1.5, 8)
    ax.set_ylim(-3, 3)
    ax.set_facecolor('#FAFAFA')

    # 视锥参数 - 使用更真实的角度
    vertex = np.array([0, 0])
    cone_angle = 0.4  # 约23度，更接近实际导引头视场
    cone_length = 6.5

    # 绘制视锥填充区域
    upper_bound = vertex + cone_length * np.array([np.cos(cone_angle), np.sin(cone_angle)])
    lower_bound = vertex + cone_length * np.array([np.cos(-cone_angle), np.sin(-cone_angle)])

    # 视锥填充
    cone_x = [vertex[0], upper_bound[0], lower_bound[0], vertex[0]]
    cone_y = [vertex[1], upper_bound[1], lower_bound[1], vertex[1]]
    ax.fill(cone_x, cone_y, color=COLORS['cone'], alpha=0.15, label='Sight Cone')

    # 视锥边界
    ax.plot([vertex[0], upper_bound[0]], [vertex[1], upper_bound[1]],
            color=COLORS['cone'], linewidth=3, label='Cone Boundary')
    ax.plot([vertex[0], lower_bound[0]], [vertex[1], lower_bound[1]],
            color=COLORS['cone'], linewidth=3)
    ax.plot([vertex[0], cone_length], [vertex[1], 0],
            color=COLORS['axis'], linewidth=2, linestyle='--', alpha=0.8, label='Cone Axis')

    # 烟幕球 - 两种情况对比
    # 情况1：有效遮蔽（左侧）
    smoke_center1 = np.array([3.2, 0.2])
    smoke_radius = 1.0

    # 计算该位置的视锥半径
    cone_radius1 = smoke_center1[0] * np.tan(cone_angle)
    d_axis1 = abs(smoke_center1[1])

    # 绘制有效遮蔽的烟幕球
    circle1 = Circle(smoke_center1, smoke_radius, fill=True,
                    facecolor=COLORS['smoke'], alpha=0.3,
                    edgecolor=COLORS['smoke'], linewidth=3, label='Effective Blocking')
    ax.add_patch(circle1)

    # 情况2：无效遮蔽（右侧）
    smoke_center2 = np.array([5.0, 1.2])
    cone_radius2 = smoke_center2[0] * np.tan(cone_angle)
    d_axis2 = abs(smoke_center2[1])

    # 绘制无效遮蔽的烟幕球
    circle2 = Circle(smoke_center2, smoke_radius, fill=True,
                    facecolor='#E67E22', alpha=0.3,
                    edgecolor='#E67E22', linewidth=3, linestyle='--', label='Insufficient Blocking')
    ax.add_patch(circle2)

    # 目标点
    target = np.array([6.5, 0])
    ax.scatter(*target, color=COLORS['target'], s=200, marker='*',
              label='Protected Target', zorder=5, edgecolors='white', linewidth=2)

    # 关键几何量标注 - 情况1（有效遮蔽）
    ax.annotate('Missile', vertex, xytext=(-0.5, -1.0), fontsize=12, ha='center',
                arrowprops=dict(arrowstyle='->', color=COLORS['missile'], lw=2),
                bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))

    # d_axis标注
    ax.plot([smoke_center1[0], smoke_center1[0]], [0, smoke_center1[1]],
            'b-', alpha=0.8, linewidth=2)
    ax.annotate(f'$d_{{axis}}$ = {d_axis1:.1f}',
                [smoke_center1[0] - 0.3, smoke_center1[1]/2],
                fontsize=11, ha='center', color='blue', weight='bold',
                bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8))

    # R_cone标注
    ax.plot([smoke_center1[0], smoke_center1[0]], [0, cone_radius1],
            'g-', alpha=0.8, linewidth=2)
    ax.annotate(f'$R_{{cone}}$ = {cone_radius1:.1f}',
                [smoke_center1[0] + 0.4, cone_radius1/2],
                fontsize=11, ha='center', color='green', weight='bold',
                bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8))

    # R_C标注
    ax.annotate(f'$R_C$ = {smoke_radius:.1f}',
                smoke_center1 + np.array([0.7, 0.7]),
                fontsize=11, ha='center', color=COLORS['smoke'], weight='bold',
                bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8))

    # 遮蔽条件标注
    condition1 = d_axis1 + cone_radius1 <= smoke_radius
    condition_text1 = f'{d_axis1:.1f} + {cone_radius1:.1f} = {d_axis1 + cone_radius1:.1f} ≤ {smoke_radius:.1f}'
    ax.text(3.2, -2.3, f'Condition: {condition_text1}',
            fontsize=10, ha='center',
            color='green' if condition1 else 'red', weight='bold',
            bbox=dict(boxstyle="round,pad=0.3",
                     facecolor='lightgreen' if condition1 else 'lightcoral', alpha=0.8))

    # 情况2的条件标注
    condition2 = d_axis2 + cone_radius2 <= smoke_radius
    condition_text2 = f'{d_axis2:.1f} + {cone_radius2:.1f} = {d_axis2 + cone_radius2:.1f} > {smoke_radius:.1f}'
    ax.text(5.0, -2.3, f'Condition: {condition_text2}',
            fontsize=10, ha='center',
            color='red', weight='bold',
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightcoral', alpha=0.8))

    ax.set_xlabel('Distance (relative units)', fontsize=12, weight='bold')
    ax.set_ylabel('Distance (relative units)', fontsize=12, weight='bold')
    ax.set_title('(a) Enhanced Blocking Condition Analysis', fontsize=13, weight='bold', pad=20)
    ax.legend(loc='upper left', fontsize=10, framealpha=0.9)
    ax.grid(True, alpha=0.4, linestyle='-', linewidth=0.5)
    ax.set_aspect('equal')

def plot_3d_overview(ax):
    """3D轨迹概览"""
    # 关键时刻
    t_key = t_det + 3
    
    # 轨迹计算
    t_range = np.linspace(0, 20, 80)
    missile_traj = np.array([P_M(t) for t in t_range])
    
    # 烟幕轨迹
    t_smoke = t_range[t_range >= t_det]
    if len(t_smoke) > 0:
        smoke_traj = np.array([P_C(t) for t in t_smoke])
        ax.plot(smoke_traj[:, 0], smoke_traj[:, 1], smoke_traj[:, 2], 
                'orange', linewidth=3, label='Smoke Trajectory', alpha=0.9)
    
    # 导弹轨迹
    ax.plot(missile_traj[:, 0], missile_traj[:, 1], missile_traj[:, 2], 
            'r-', linewidth=2, label='Missile Trajectory', alpha=0.8)
    
    # 关键点标记
    ax.scatter(*P_M0, color='red', s=80, marker='^', label='M1 Start')
    ax.scatter(*P_U0, color='blue', s=80, marker='s', label='FY1 Start')
    ax.scatter(*P_T_center, color='purple', s=120, marker='*', label='Target')
    
    # 当前位置
    if t_key >= t_det:
        current_smoke = P_C(t_key)
        ax.scatter(*current_smoke, color='orange', s=100, marker='o', alpha=0.8)
    
    # 目标圆柱体轮廓
    theta = np.linspace(0, 2*np.pi, 16)
    x_cyl = P_T_bottom[0] + R_T * np.cos(theta)
    y_cyl = P_T_bottom[1] + R_T * np.sin(theta)
    z_bottom = np.full_like(x_cyl, P_T_bottom[2])
    z_top = np.full_like(x_cyl, P_T_bottom[2] + H_T)
    
    ax.plot(x_cyl, y_cyl, z_bottom, 'purple', alpha=0.5, linewidth=1)
    ax.plot(x_cyl, y_cyl, z_top, 'purple', alpha=0.5, linewidth=1)
    
    ax.set_xlabel('X (m)')
    ax.set_ylabel('Y (m)')
    ax.set_zlabel('Z (m)')
    ax.set_title('(b) 3D Scenario Overview')
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
    ax.view_init(elev=15, azim=45)

def plot_blocking_effectiveness(ax):
    """遮蔽效果分析"""
    # 时间范围
    t_range = np.linspace(t_det, t_det + T_eff, 800)
    blocking_status = [is_blocked(t) for t in t_range]
    
    # 绘制遮蔽状态
    ax.fill_between(t_range, 0, blocking_status, alpha=0.6, color='lightgreen', 
                    label='Effective Blocking Period')
    ax.plot(t_range, blocking_status, 'darkgreen', linewidth=2)
    
    # 计算关键数值
    dt = t_range[1] - t_range[0]
    total_time = sum(blocking_status) * dt
    efficiency = total_time / T_eff * 100
    
    # 添加数值标注
    textstr = f'Total Blocking Time: {total_time:.2f}s\nBlocking Efficiency: {efficiency:.1f}%'
    props = dict(boxstyle='round', facecolor='wheat', alpha=0.8)
    ax.text(0.05, 0.95, textstr, transform=ax.transAxes, fontsize=10,
            verticalalignment='top', bbox=props)
    
    # 时间标记
    ax.axvline(x=t_det, color='red', linestyle='--', alpha=0.7, label='Detonation')
    ax.axvline(x=t_det + T_eff, color='red', linestyle='--', alpha=0.7, label='Expiration')
    
    ax.set_xlabel('Time (s)')
    ax.set_ylabel('Blocking Status')
    ax.set_title('(c) Blocking Effectiveness Analysis')
    ax.set_ylim(-0.05, 1.05)
    ax.legend(fontsize=9)
    ax.grid(True, alpha=0.3)

def plot_key_parameters(ax):
    """关键参数演化"""
    t_range = np.linspace(t_det, t_det + T_eff, 150)
    
    d_axis_vals = []
    r_cone_vals = []
    sum_vals = []
    
    for t in t_range:
        V = P_M(t)
        A = view_cone_axis(t)
        theta = view_cone_half_angle(t)
        C = P_C(t)
        
        L = sub(C, V)
        d_along = dot(L, A)
        
        if d_along > 0:
            L2 = dot(L, L)
            perp2 = max(L2 - d_along * d_along, 0.0)
            d_axis = sqrt(perp2)
            R_cone = d_along * tan(theta)
            sum_val = d_axis + R_cone
        else:
            d_axis = np.nan
            R_cone = 0
            sum_val = np.nan
            
        d_axis_vals.append(d_axis)
        r_cone_vals.append(R_cone)
        sum_vals.append(sum_val)
    
    # 绘制参数曲线
    ax.plot(t_range, d_axis_vals, 'b-', linewidth=2, label='d_axis(t)')
    ax.plot(t_range, r_cone_vals, 'r-', linewidth=2, label='R_cone(t)')
    ax.plot(t_range, sum_vals, 'purple', linewidth=2, label='d_axis + R_cone')
    ax.axhline(y=R_C, color='orange', linestyle='--', linewidth=2, label='R_C (Smoke Radius)')
    
    # 遮蔽条件区域
    valid_mask = ~np.isnan(sum_vals)
    blocked_mask = np.array(sum_vals) <= R_C
    combined_mask = valid_mask & blocked_mask
    
    if np.any(combined_mask):
        ax.fill_between(t_range, 0, 12, where=combined_mask, alpha=0.2, color='green', 
                        label='Blocking Condition Met')
    
    ax.set_xlabel('Time (s)')
    ax.set_ylabel('Distance (m)')
    ax.set_title('(d) Key Parameter Evolution')
    ax.legend(fontsize=9)
    ax.grid(True, alpha=0.3)
    ax.set_ylim(0, 12)

def create_model_flowchart():
    """创建模型建立流程图"""
    fig, ax = plt.subplots(1, 1, figsize=(10, 7))
    
    # 流程步骤
    steps = [
        {'pos': (2, 6), 'text': 'Coordinate System\n& Parameters', 'color': 'lightblue'},
        {'pos': (5, 6), 'text': 'Entity Kinematics\nModeling', 'color': 'lightgreen'},
        {'pos': (8, 6), 'text': 'Geometric Blocking\nModel', 'color': 'lightyellow'},
        {'pos': (3.5, 3.5), 'text': 'Sight Cone\nConstruction', 'color': 'lightcoral'},
        {'pos': (6.5, 3.5), 'text': 'Cone-Sphere\nIntersection', 'color': 'lightpink'},
        {'pos': (5, 1), 'text': 'Blocking Duration\nCalculation', 'color': 'lightgray'},
    ]
    
    # 绘制流程框
    for step in steps:
        rect = FancyBboxPatch((step['pos'][0]-0.7, step['pos'][1]-0.4), 1.4, 0.8, 
                             boxstyle="round,pad=0.08", facecolor=step['color'], 
                             edgecolor='black', linewidth=1.2)
        ax.add_patch(rect)
        ax.text(step['pos'][0], step['pos'][1], step['text'], ha='center', va='center', 
                fontsize=10, fontweight='bold')
    
    # 连接箭头
    arrows = [
        ((2.7, 6), (4.3, 6)),
        ((5.7, 6), (7.3, 6)),
        ((5, 5.6), (3.8, 3.9)),
        ((8, 5.6), (6.8, 3.9)),
        ((3.8, 3.1), (4.7, 1.4)),
        ((6.2, 3.1), (5.3, 1.4)),
    ]
    
    for start, end in arrows:
        ax.annotate('', xy=end, xytext=start,
                   arrowprops=dict(arrowstyle='->', lw=1.8, color='darkblue'))
    
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 7)
    ax.set_title('Model Development Flowchart', fontsize=14, fontweight='bold')
    ax.axis('off')
    
    return fig

if __name__ == "__main__":
    # 生成关键图例
    print("Generating key figures for Problem 1...")
    
    # 主要分析图
    fig1 = create_key_figures()
    fig1.savefig('problem1_key_analysis.png', dpi=300, bbox_inches='tight', facecolor='white')
    fig1.savefig('problem1_key_analysis.pdf', bbox_inches='tight', facecolor='white')
    
    # 模型流程图
    fig2 = create_model_flowchart()
    fig2.savefig('problem1_model_flowchart.png', dpi=300, bbox_inches='tight', facecolor='white')
    fig2.savefig('problem1_model_flowchart.pdf', bbox_inches='tight', facecolor='white')
    
    # 生成数值报告
    print("\n" + "="*50)
    print("PROBLEM 1 NUMERICAL ANALYSIS REPORT")
    print("="*50)
    
    P_d, P_det = sanity_check_points()
    total_time = compute_effective遮蔽时长(0.01)
    
    print(f"Drop Point: ({P_d[0]:.1f}, {P_d[1]:.1f}, {P_d[2]:.1f}) m")
    print(f"Detonation Point: ({P_det[0]:.1f}, {P_det[1]:.1f}, {P_det[2]:.1f}) m")
    print(f"Total Effective Blocking Time: {total_time:.3f} seconds")
    print(f"Blocking Efficiency: {total_time/T_eff*100:.1f}%")
    print("="*50)
    
    print("\nGenerated files:")
    print("1. problem1_key_analysis.png/pdf - Main analysis figure")
    print("2. problem1_model_flowchart.png/pdf - Model development flowchart")
    
    plt.show()
