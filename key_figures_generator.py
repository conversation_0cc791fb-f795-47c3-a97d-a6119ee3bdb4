"""
关键图例生成器 - 问题1核心图表
解决中文字体问题，生成排版美观的关键图例
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, Circle
import warnings
warnings.filterwarnings('ignore')

# 解决中文字体问题
import matplotlib
matplotlib.rcParams['font.family'] = ['DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 11
plt.rcParams['axes.linewidth'] = 1.0
plt.rcParams['lines.linewidth'] = 1.8

# 导入问题1的核心函数
from problem1 import *

def create_key_figures():
    """创建关键图例 - 2x2布局"""
    
    fig = plt.figure(figsize=(12, 9))
    fig.suptitle('Problem 1: Smoke Grenade Deployment Strategy Analysis', 
                 fontsize=14, fontweight='bold', y=0.95)
    
    # 图1：几何模型原理图
    ax1 = plt.subplot(2, 2, 1)
    plot_geometric_principle(ax1)
    
    # 图2：3D轨迹概览
    ax2 = plt.subplot(2, 2, 2, projection='3d')
    plot_3d_overview(ax2)
    
    # 图3：遮蔽效果分析
    ax3 = plt.subplot(2, 2, 3)
    plot_blocking_effectiveness(ax3)
    
    # 图4：关键参数演化
    ax4 = plt.subplot(2, 2, 4)
    plot_key_parameters(ax4)
    
    plt.tight_layout(rect=[0, 0.03, 1, 0.93])
    return fig

def plot_geometric_principle(ax):
    """几何模型原理图"""
    ax.set_xlim(-1, 7)
    ax.set_ylim(-2.5, 2.5)
    
    # 视锥参数
    vertex = np.array([0, 0])
    cone_angle = 0.5
    cone_length = 5.5
    
    # 绘制视锥
    upper_bound = vertex + cone_length * np.array([np.cos(cone_angle), np.sin(cone_angle)])
    lower_bound = vertex + cone_length * np.array([np.cos(-cone_angle), np.sin(-cone_angle)])
    
    ax.plot([vertex[0], upper_bound[0]], [vertex[1], upper_bound[1]], 'r-', linewidth=2, label='Sight Cone Boundary')
    ax.plot([vertex[0], lower_bound[0]], [vertex[1], lower_bound[1]], 'r-', linewidth=2)
    ax.plot([vertex[0], cone_length], [vertex[1], 0], 'r--', linewidth=1, alpha=0.7, label='Cone Axis')
    
    # 烟幕球
    smoke_center = np.array([3.5, 0.3])
    smoke_radius = 0.8
    circle = Circle(smoke_center, smoke_radius, fill=False, color='orange', linewidth=2.5, label='Smoke Sphere')
    ax.add_patch(circle)
    
    # 目标点
    target = np.array([5.5, 0])
    ax.scatter(*target, color='purple', s=150, marker='*', label='Protected Target', zorder=5)
    
    # 关键几何量标注
    ax.annotate('Missile', vertex, xytext=(-0.3, -0.8), fontsize=10, ha='center',
                arrowprops=dict(arrowstyle='->', color='red', lw=1))
    ax.annotate('R_C', smoke_center + np.array([0.6, 0.6]), fontsize=11, ha='center', color='orange')
    
    # 距离标注
    ax.plot([smoke_center[0], smoke_center[0]], [0, smoke_center[1]], 'b--', alpha=0.6, linewidth=1)
    ax.annotate('d_axis', [smoke_center[0], -1.8], fontsize=11, ha='center', color='blue')
    
    # 视锥半径标注
    cone_radius_point = np.array([smoke_center[0], cone_length * np.tan(cone_angle) * smoke_center[0] / cone_length])
    ax.plot([smoke_center[0], cone_radius_point[0]], [0, cone_radius_point[1]], 'g--', alpha=0.6, linewidth=1)
    ax.annotate('R_cone', [smoke_center[0] + 0.3, cone_radius_point[1] + 0.2], fontsize=11, ha='center', color='green')
    
    ax.set_xlabel('Distance (relative units)')
    ax.set_ylabel('Distance (relative units)')
    ax.set_title('(a) Cone-Sphere Intersection Model')
    ax.legend(loc='upper left', fontsize=9)
    ax.grid(True, alpha=0.3)
    ax.set_aspect('equal')

def plot_3d_overview(ax):
    """3D轨迹概览"""
    # 关键时刻
    t_key = t_det + 3
    
    # 轨迹计算
    t_range = np.linspace(0, 20, 80)
    missile_traj = np.array([P_M(t) for t in t_range])
    
    # 烟幕轨迹
    t_smoke = t_range[t_range >= t_det]
    if len(t_smoke) > 0:
        smoke_traj = np.array([P_C(t) for t in t_smoke])
        ax.plot(smoke_traj[:, 0], smoke_traj[:, 1], smoke_traj[:, 2], 
                'orange', linewidth=3, label='Smoke Trajectory', alpha=0.9)
    
    # 导弹轨迹
    ax.plot(missile_traj[:, 0], missile_traj[:, 1], missile_traj[:, 2], 
            'r-', linewidth=2, label='Missile Trajectory', alpha=0.8)
    
    # 关键点标记
    ax.scatter(*P_M0, color='red', s=80, marker='^', label='M1 Start')
    ax.scatter(*P_U0, color='blue', s=80, marker='s', label='FY1 Start')
    ax.scatter(*P_T_center, color='purple', s=120, marker='*', label='Target')
    
    # 当前位置
    if t_key >= t_det:
        current_smoke = P_C(t_key)
        ax.scatter(*current_smoke, color='orange', s=100, marker='o', alpha=0.8)
    
    # 目标圆柱体轮廓
    theta = np.linspace(0, 2*np.pi, 16)
    x_cyl = P_T_bottom[0] + R_T * np.cos(theta)
    y_cyl = P_T_bottom[1] + R_T * np.sin(theta)
    z_bottom = np.full_like(x_cyl, P_T_bottom[2])
    z_top = np.full_like(x_cyl, P_T_bottom[2] + H_T)
    
    ax.plot(x_cyl, y_cyl, z_bottom, 'purple', alpha=0.5, linewidth=1)
    ax.plot(x_cyl, y_cyl, z_top, 'purple', alpha=0.5, linewidth=1)
    
    ax.set_xlabel('X (m)')
    ax.set_ylabel('Y (m)')
    ax.set_zlabel('Z (m)')
    ax.set_title('(b) 3D Scenario Overview')
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
    ax.view_init(elev=15, azim=45)

def plot_blocking_effectiveness(ax):
    """遮蔽效果分析"""
    # 时间范围
    t_range = np.linspace(t_det, t_det + T_eff, 800)
    blocking_status = [is_blocked(t) for t in t_range]
    
    # 绘制遮蔽状态
    ax.fill_between(t_range, 0, blocking_status, alpha=0.6, color='lightgreen', 
                    label='Effective Blocking Period')
    ax.plot(t_range, blocking_status, 'darkgreen', linewidth=2)
    
    # 计算关键数值
    dt = t_range[1] - t_range[0]
    total_time = sum(blocking_status) * dt
    efficiency = total_time / T_eff * 100
    
    # 添加数值标注
    textstr = f'Total Blocking Time: {total_time:.2f}s\nBlocking Efficiency: {efficiency:.1f}%'
    props = dict(boxstyle='round', facecolor='wheat', alpha=0.8)
    ax.text(0.05, 0.95, textstr, transform=ax.transAxes, fontsize=10,
            verticalalignment='top', bbox=props)
    
    # 时间标记
    ax.axvline(x=t_det, color='red', linestyle='--', alpha=0.7, label='Detonation')
    ax.axvline(x=t_det + T_eff, color='red', linestyle='--', alpha=0.7, label='Expiration')
    
    ax.set_xlabel('Time (s)')
    ax.set_ylabel('Blocking Status')
    ax.set_title('(c) Blocking Effectiveness Analysis')
    ax.set_ylim(-0.05, 1.05)
    ax.legend(fontsize=9)
    ax.grid(True, alpha=0.3)

def plot_key_parameters(ax):
    """关键参数演化"""
    t_range = np.linspace(t_det, t_det + T_eff, 150)
    
    d_axis_vals = []
    r_cone_vals = []
    sum_vals = []
    
    for t in t_range:
        V = P_M(t)
        A = view_cone_axis(t)
        theta = view_cone_half_angle(t)
        C = P_C(t)
        
        L = sub(C, V)
        d_along = dot(L, A)
        
        if d_along > 0:
            L2 = dot(L, L)
            perp2 = max(L2 - d_along * d_along, 0.0)
            d_axis = sqrt(perp2)
            R_cone = d_along * tan(theta)
            sum_val = d_axis + R_cone
        else:
            d_axis = np.nan
            R_cone = 0
            sum_val = np.nan
            
        d_axis_vals.append(d_axis)
        r_cone_vals.append(R_cone)
        sum_vals.append(sum_val)
    
    # 绘制参数曲线
    ax.plot(t_range, d_axis_vals, 'b-', linewidth=2, label='d_axis(t)')
    ax.plot(t_range, r_cone_vals, 'r-', linewidth=2, label='R_cone(t)')
    ax.plot(t_range, sum_vals, 'purple', linewidth=2, label='d_axis + R_cone')
    ax.axhline(y=R_C, color='orange', linestyle='--', linewidth=2, label='R_C (Smoke Radius)')
    
    # 遮蔽条件区域
    valid_mask = ~np.isnan(sum_vals)
    blocked_mask = np.array(sum_vals) <= R_C
    combined_mask = valid_mask & blocked_mask
    
    if np.any(combined_mask):
        ax.fill_between(t_range, 0, 12, where=combined_mask, alpha=0.2, color='green', 
                        label='Blocking Condition Met')
    
    ax.set_xlabel('Time (s)')
    ax.set_ylabel('Distance (m)')
    ax.set_title('(d) Key Parameter Evolution')
    ax.legend(fontsize=9)
    ax.grid(True, alpha=0.3)
    ax.set_ylim(0, 12)

def create_model_flowchart():
    """创建模型建立流程图"""
    fig, ax = plt.subplots(1, 1, figsize=(10, 7))
    
    # 流程步骤
    steps = [
        {'pos': (2, 6), 'text': 'Coordinate System\n& Parameters', 'color': 'lightblue'},
        {'pos': (5, 6), 'text': 'Entity Kinematics\nModeling', 'color': 'lightgreen'},
        {'pos': (8, 6), 'text': 'Geometric Blocking\nModel', 'color': 'lightyellow'},
        {'pos': (3.5, 3.5), 'text': 'Sight Cone\nConstruction', 'color': 'lightcoral'},
        {'pos': (6.5, 3.5), 'text': 'Cone-Sphere\nIntersection', 'color': 'lightpink'},
        {'pos': (5, 1), 'text': 'Blocking Duration\nCalculation', 'color': 'lightgray'},
    ]
    
    # 绘制流程框
    for step in steps:
        rect = FancyBboxPatch((step['pos'][0]-0.7, step['pos'][1]-0.4), 1.4, 0.8, 
                             boxstyle="round,pad=0.08", facecolor=step['color'], 
                             edgecolor='black', linewidth=1.2)
        ax.add_patch(rect)
        ax.text(step['pos'][0], step['pos'][1], step['text'], ha='center', va='center', 
                fontsize=10, fontweight='bold')
    
    # 连接箭头
    arrows = [
        ((2.7, 6), (4.3, 6)),
        ((5.7, 6), (7.3, 6)),
        ((5, 5.6), (3.8, 3.9)),
        ((8, 5.6), (6.8, 3.9)),
        ((3.8, 3.1), (4.7, 1.4)),
        ((6.2, 3.1), (5.3, 1.4)),
    ]
    
    for start, end in arrows:
        ax.annotate('', xy=end, xytext=start,
                   arrowprops=dict(arrowstyle='->', lw=1.8, color='darkblue'))
    
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 7)
    ax.set_title('Model Development Flowchart', fontsize=14, fontweight='bold')
    ax.axis('off')
    
    return fig

if __name__ == "__main__":
    # 生成关键图例
    print("Generating key figures for Problem 1...")
    
    # 主要分析图
    fig1 = create_key_figures()
    fig1.savefig('problem1_key_analysis.png', dpi=300, bbox_inches='tight', facecolor='white')
    fig1.savefig('problem1_key_analysis.pdf', bbox_inches='tight', facecolor='white')
    
    # 模型流程图
    fig2 = create_model_flowchart()
    fig2.savefig('problem1_model_flowchart.png', dpi=300, bbox_inches='tight', facecolor='white')
    fig2.savefig('problem1_model_flowchart.pdf', bbox_inches='tight', facecolor='white')
    
    # 生成数值报告
    print("\n" + "="*50)
    print("PROBLEM 1 NUMERICAL ANALYSIS REPORT")
    print("="*50)
    
    P_d, P_det = sanity_check_points()
    total_time = compute_effective遮蔽时长(0.01)
    
    print(f"Drop Point: ({P_d[0]:.1f}, {P_d[1]:.1f}, {P_d[2]:.1f}) m")
    print(f"Detonation Point: ({P_det[0]:.1f}, {P_det[1]:.1f}, {P_det[2]:.1f}) m")
    print(f"Total Effective Blocking Time: {total_time:.3f} seconds")
    print(f"Blocking Efficiency: {total_time/T_eff*100:.1f}%")
    print("="*50)
    
    print("\nGenerated files:")
    print("1. problem1_key_analysis.png/pdf - Main analysis figure")
    print("2. problem1_model_flowchart.png/pdf - Model development flowchart")
    
    plt.show()
