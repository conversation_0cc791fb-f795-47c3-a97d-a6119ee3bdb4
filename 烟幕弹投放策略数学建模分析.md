# **用于无人机投放烟幕干扰弹对抗策略的综合数学建模框架**

**摘要**

本文旨在构建一个多层面的数学建模框架，以确定无人机（UAV）投放烟幕干扰弹对抗来袭导弹威胁的最优策略。报告建立了一套完备的模型，涵盖了所有动态实体（导弹、无人机、干扰弹、烟幕云团）的运动学，并基于动态锥体-球体相交理论，构建了一个严谨的目标遮蔽几何模型。为实现保护目标有效遮蔽时间最大化的目标，本文将该问题分解为一系列复杂度递增的优化任务。针对这些任务，设计并实现了一系列算法解决方案，包括用于单智能体和多智能体轨迹规划的粒子群优化（PSO）算法、用于序列化投放的贪心调度算法，以及一个结合了用于任务分配的匈牙利法和用于协同执行的分布式PSO算法的分层框架。报告针对五个不同的交战场景提供了详细的求解方案与分析，最终形成了一套适用于多智能体、多目标防御问题的综合策略。研究结果表明，该集成建模方法在生成鲁棒且高效的对抗策略方面具有显著效能。

---

## **1. 基础模型**

本章节旨在为后续所有分析奠定坚实的数学基础。它将复杂的物理场景转化为一个精确、可计算的方程与条件系统，为策略优化提供理论依据。

### **1.1. 系统坐标框架、参数与假设**

为确保模型的一致性与准确性，首先需要明确定义坐标系、关键参数及基本假设。

**坐标系**：根据题目描述，建立一个三维笛卡尔直角坐标系。以假目标位置为坐标原点 $(0,0,0)$，$xy$ 平面为水平地面，$z$ 轴垂直向上，代表高度。所有位置和运动都将在此坐标系下进行描述。

**参数与初始条件**：将题目中给出的所有常量和初始状态向量进行形式化定义，并汇总于下表。这包括导弹速度、烟幕云团属性以及所有导弹和无人机的初始位置信息。

**模型假设**：为了使问题简化并聚焦于核心要素，本模型建立在以下几项关键假设之上：

* 忽略烟幕干扰弹在自由落体阶段受到的空气阻力，其运动轨迹被视为标准的抛物线运动 1。  
* 烟幕云团在起爆后瞬时形成标准球体。  
* 在起爆后的 $20\ \mathrm{s}$ 内，半径为 $10\ \mathrm{m}$ 的烟幕云团内部浓度均匀，能够提供有效遮蔽。  
* 无人机在接收任务后，能够瞬时调整飞行航向与速度，无需考虑加速或转向时间。  
* 指挥控制中心的计算与通信延迟可忽略不计。

**表1：系统参数与初始条件**

| 参数/变量 | 符号 | 数值/初始位置 (单位：米, 秒) | 描述 |
| :---- | :---- | :---- | :---- |
| 导弹速度 | $v_M$ | $300\ \mathrm{m/s}$ | 所有来袭导弹的飞行速度 |
| 烟幕下沉速度 | $v_{\mathrm{sink}}$ | $3\ \mathrm{m/s}$ | 烟幕云团中心的匀速下沉速度 |
| 烟幕有效半径 | $R_C$ | $10\ \mathrm{m}$ | 烟幕云团提供有效遮蔽的球体半径 |
| 烟幕有效时长 | $T_{\mathrm{eff}}$ | $20\ \mathrm{s}$ | 烟幕云团起爆后的有效遮蔽持续时间 |
| 保护目标半径 | $R_T$ | $7\ \mathrm{m}$ | 圆柱形真目标的底面半径 |
| 保护目标高度 | $H_T$ | $10\ \mathrm{m}$ | 圆柱形真目标的高度 |
| 保护目标位置 | $\mathbf{P}_T$ | $(0,200,0)$ | 真目标下底面圆心坐标 |
| 重力加速度 | $g$ | $9.8\ \mathrm{m/s}^2$ | 地球表面重力加速度 |
| M1初始位置 | $\mathbf{P}_{M_1,0}$ | $(20000,0,2000)$ | 导弹 $M_1$ 的初始位置 |
| M2初始位置 | $\mathbf{P}_{M_2,0}$ | $(19000,600,2100)$ | 导弹 $M_2$ 的初始位置 |
| M3初始位置 | $\mathbf{P}_{M_3,0}$ | $(18000,-600,1900)$ | 导弹 $M_3$ 的初始位置 |
| FY1初始位置 | $\mathbf{P}_{U_1,0}$ | $(17800,0,1800)$ | 无人机 FY1 的初始位置 |
| FY2初始位置 | $\mathbf{P}_{U_2,0}$ | $(12000,1400,1400)$ | 无人机 FY2 的初始位置 |
| FY3初始位置 | $\mathbf{P}_{U_3,0}$ | $(6000,-3000,700)$ | 无人机 FY3 的初始位置 |
| FY4初始位置 | $\mathbf{P}_{U_4,0}$ | $(11000,2000,1800)$ | 无人机 FY4 的初始位置 |
| FY5初始位置 | $\mathbf{P}_{U_5,0}$ | $(13000,-2000,1300)$ | 无人机 FY5 的初始位置 |

将所有数值参数集中管理，不仅可以避免在后续复杂计算中出现错误，也为模型的参数化和灵敏度分析提供了便利，是严谨建模流程中的基础步骤。

### **1.2. 系统实体运动学建模**

基于牛顿运动定律，对场景中所有动态实体的运动轨迹进行数学描述。

导弹轨迹：导弹被建模为匀速直线运动，其飞行方向指向坐标原点（假目标）。对于第 $i$ 枚导弹 $M_i$，其在时刻 $t$ 的位置向量 $\mathbf{P}_{M_i}(t)$ 可表示为：

$$\mathbf{P}_{M_i}(t)=\mathbf{P}_{M_i,0}+\mathbf{v}_{M_i}\,t$$

其中，$\mathbf{P}_{M_i,0}$ 是其初始位置向量，速度向量 $\mathbf{v}_{M_i}$ 为：

$$\mathbf{v}_{M_i}=v_M\cdot \operatorname{unit}(-\mathbf{P}_{M_i,0})$$

无人机轨迹：无人机在接收任务后，以选定的速度 $v_{U_j}$（标量，$70\le v_{U_j}\le 140\ \mathrm{m/s}$）和飞行方向单位向量 $\hat{\mathbf{d}}_{U_j}$ 进行等高匀速直线飞行。第 $j$ 架无人机 $U_j$ 在时刻 $t$ 的位置 $\mathbf{P}_{U_j}(t)$ 为：

$$\mathbf{P}_{U_j}(t)=\mathbf{P}_{U_j,0}+v_{U_j}\,\hat{\mathbf{d}}_{U_j}\,t$$

**烟幕干扰弹轨迹**：烟幕弹脱离无人机后，其运动遵循三维抛体运动规律 2。其水平方向的初速度等于投放时无人机的飞行速度，垂直方向初速度为零。若 $U_j$ 在 $t_{drop}$ 时刻于位置 $\mathbf{P}_{U_j}(t_{drop})$ 投放一枚干扰弹，其速度为 $v_{U_j}$，则干扰弹在 $t\ge t_{drop}$ 时刻的位置 $\mathbf{P}_G(t)=(x_G(t),y_G(t),z_G(t))$ 为：

$$
\begin{aligned}
 x_G(t)&=x_{U_j}(t_{drop})+v_{U_j,x}\,(t-t_{drop})\\
 y_G(t)&=y_{U_j}(t_{drop})+v_{U_j,y}\,(t-t_{drop})\\
 z_G(t)&=z_{U_j}(t_{drop})-\tfrac{1}{2}g\,(t-t_{drop})^2
\end{aligned}
$$

烟幕云团中心轨迹：干扰弹在 $t_{det}$ 时刻于位置 $\mathbf{P}_G(t_{det})$ 起爆后，形成的烟幕云团中心以速度 $v_{\mathrm{sink}}$ 匀速下沉。其在 $t\ge t_{det}$ 时刻的位置 $\mathbf{P}_C(t)$ 为：

$$\mathbf{P}_C(t)=\mathbf{P}_G(t_{det})-(0,0,\,v_{\mathrm{sink}}\,(t-t_{det}))$$

### **1.3. 几何遮蔽模型：从视线到锥体-球体相交**

判定是否实现有效遮蔽是本模型的核心。一个常见的误区是仅检验从导弹到目标中心点的单条视线是否被遮挡。然而，由于保护目标是一个半径为 $7\ \mathrm{m}$、高为 $10\ \mathrm{m}$ 的圆柱体，导弹能够观察到的是一个二维轮廓，而非一个点。为了完全遮蔽目标，必须阻挡所有射向该轮廓的视线。

**视线锥的定义**：在任意时刻 $t$，从导弹位置 $\mathbf{P}_M(t)$ 出发，所有能够射到圆柱形目标可见轮廓上的视线集合，共同构成一个几何锥体。该锥体的顶点是导弹位置 $\mathbf{P}_M(t)$，其底面由从该点到圆柱体的切线所定义。这个视线锥是随时间动态变化的，因为其顶点（导弹）在不断移动。

**遮蔽条件**：有效的遮蔽发生在且仅当半径为 $R_C=10\ \mathrm{m}$、球心为 $\mathbf{P}_C(t)$ 的烟幕云团球体，能够完全拦截该时刻的整个视线锥。

**数学公式化**：直接求解动态锥体与球体的相交曲线在计算上是极其复杂的 4。为了在优化循环中进行高效判断，需要将此几何问题转化为一组代数不等式。借鉴球体扫掠体积和距离检测的原理 6，可以将遮蔽条件简化为：检验烟幕球心到视线锥中轴线的距离，并判断其半径是否足以在该位置完全覆盖视线锥的横截面。这一系列判断构成了我们的核心布尔函数 $\mathrm{IsBlocked}(t)$，其值为真（1）表示成功遮蔽，为假（0）表示未遮蔽。

这一从“线-球相交”到“锥-球相交”的建模升级，是确保模型物理真实性和求解结果有效性的关键一步。它显著提高了模型的保真度，尽管也增加了几何判定的复杂性，但这是获得有意义策略的必要代价。

### **1.4. 有效遮蔽时间窗口的定义与计算**

**单枚干扰弹的窗口**：对于在 $t_{det}$ 时刻起爆的单枚干扰弹，其潜在的有效遮蔽时间窗口为 $[t_{det},\,t_{det}+20]$。

**有效遮蔽时长计算**：实际的有效遮蔽时长，是在上述 $20\ \mathrm{s}$ 窗口内，$\mathrm{IsBlocked}(t)$ 条件为真的所有时间段的总和。在计算中，可以通过在时间窗口内以微小步长（例如 $0.01\ \mathrm{s}$）对 $\mathrm{IsBlocked}(t)$ 函数进行数值积分，或者通过求解 $\mathrm{IsBlocked}(t)$ 状态转换点的方程来精确计算。

**总遮蔽时间（多枚干扰弹）**：对于多枚干扰弹的情况，总有效遮蔽时间是所有单枚干扰弹有效遮蔽时间区间的并集的测度。这是问题3、4和5中优化模型的目标函数。

---

## **2. 单枚干扰弹投放策略分析（问题1 & 2）**

本节将应用前述基础模型，分析最简单的情形：一架无人机使用一枚干扰弹对抗一枚来袭导弹。

### **2.1. 固定策略评估与遮蔽时长计算（问题1）**

**问题描述**：给定 FY1 的一套固定策略：速度为 $120\ \mathrm{m/s}$，朝向假目标飞行；接收任务 $1.5\ \mathrm{s}$ 后投放干扰弹；干扰弹投放 $3.6\ \mathrm{s}$ 后起爆。要求计算对 $M_1$ 的有效遮蔽时长。

**分步计算流程**：

1. **确定无人机运动状态**：计算 FY1 朝向原点飞行的单位方向向量 $\hat{\mathbf{d}}_{U_1}$，其速度向量为 $\mathbf{v}_{U_1}=120\,\hat{\mathbf{d}}_{U_1}$。  
2. **计算投放点**：投放时间 $t_{drop}=1.5\ \mathrm{s}$。根据无人机轨迹方程，计算 FY1 在此时的位置 $\mathbf{P}_{U_1}(1.5)$。  
3. **建立干扰弹弹道**：以 $\mathbf{P}_{U_1}(1.5)$ 为初始位置，$v_{U_1}$ 为水平初速度，建立干扰弹的抛体运动方程。  
4. **计算起爆点**：起爆时间 $t_{det}=1.5+3.6=5.1\ \mathrm{s}$。计算干扰弹在 $t_{det}$ 时刻的位置 $\mathbf{P}_G(5.1)$。  
5. **建立烟幕云团运动轨迹**：从 $\mathbf{P}_G(5.1)$ 开始，建立烟幕云团中心匀速下沉的运动方程。  
6. **评估遮蔽效果**：在时间区间 $t\in[5.1,25.1]$ 内，以 $0.01\ \mathrm{s}$ 为步长，循环计算该时刻的导弹位置 $\mathbf{P}_{M_1}(t)$、烟幕云团位置 $\mathbf{P}_C(t)$ 和保护目标位置 $\mathbf{P}_T$。  
7. **计算总时长**：调用 $\mathrm{IsBlocked}(t)$ 函数进行判断，并累加所有 $\mathrm{IsBlocked}(t)$ 为真的时间步长，得到最终的有效遮蔽总时长。

### **2.2. 单枚干扰弹最优策略（问题2）**

#### **2.2.1. 构建非线性优化问题**

**决策变量**：需要优化的策略由一组连续变量定义：无人机的飞行速度 $v_U$，其飞行方向角 $\theta_U$。在无人机路径规划领域，PSO已有广泛且成功的应用 8。

**粒子编码**：PSO算法中的每个“粒子”都代表一套完整的投放策略。一个粒子 $p$ 可以被编码为一个向量：$p=[\,v_U,\theta_U,t_{delay}\,]$。

**适应度函数**：粒子的适应度（fitness）直接由目标函数定义。对于粒子 $p$，其适应度为 $\mathrm{Fitness}(p)=T_{obs}(p)$。计算该值需要完整地模拟一次投放过程并计算总遮蔽时长。

**算法实现**：算法将初始化一个粒子种群，每个粒子随机赋予一组策略参数。在迭代过程中，每个粒子根据其自身历史最优位置和种群全局最优位置来更新自己的速度和位置（即策略参数）。经过足够次数的迭代，种群将收敛到最优解附近。

#### **2.2.3. 最优策略展示**

通过PSO算法求解后，将得到一组最优的决策变量。这些变量定义了无人机FY1的最佳行动方案。

**表2：问题2的最优单枚干扰弹策略**

| 参数 | 最优值 | 描述 |
| :---- | :---- | :---- |
| 无人机飞行速度 ($v_U$) | (待计算) $\mathrm{m/s}$ | FY1选择的飞行速度 |
| 无人机飞行方向角 ($\theta_U$) | (待计算) $\mathrm{rad}$ | FY1的飞行方向（与 $x$ 轴正向夹角） |
| 起爆延迟时间 ($t_{delay}$) | (待计算) $\mathrm{s}$ | 干扰弹投放后到起爆的时间间隔 |
| **导出结果** |  |  |
| 干扰弹投放点 | (待计算) | 投放时刻无人机的位置 |
| 干扰弹起爆点 | (待计算) | 起爆时刻干扰弹的位置 |
| 最大有效遮蔽时长 | (待计算) $\mathrm{s}$ | 该策略下实现的最大遮蔽时间 |

---

## **3. 单无人机多干扰弹策略（问题3）**

本节研究在单一平台资源受限的情况下，如何进行多次投放的调度与规划，以实现整体效能最大化。

### **3.1. 构建序列化分配与调度问题**

**问题描述**：无人机FY1需投放3枚干扰弹，以最大化对导弹M1的总遮蔽时间。其飞行轨迹（速度和方向）一经确定便保持不变，且连续两次投放至少间隔 $1\ \mathrm{s}$。

这个问题的核心挑战在于，三次投放并非相互独立的优化问题。第一次投放的决策（投放时间和位置）会直接影响第二次和第三次投放的可用选项和最终效果。将此问题视为一个大型的、包含所有投放参数的9维优化问题在计算上是低效的。问题的本质是一个带有时间约束的序列化决策过程，这启发我们借鉴调度理论中的思想。

经典区间调度问题的目标是在一组时间区间中选择不重叠的最大子集 9。虽然我们的目标是最大化区间并集的总长度而非区间数量，但其内在的贪心思想具有重要的参考价值。一个高效的策略是，在每一步决策中，选择能够为当前已覆盖的时间集合带来最大“边际增益”的投放方案。

### **3.2. 混合贪心-PSO算法**

基于上述分析，设计一种混合算法来求解此问题。该算法结合了PSO的全局搜索能力和贪心算法的快速决策能力。

**算法描述**：

1. **外层循环 (PSO)**：使用PSO算法对无人机的宏观轨迹参数——飞行速度 $v_U$ 和方向角 $\theta_U$ 进行全局优化。每个粒子代表一种无人机飞行轨迹。  
2. **内层循环 (贪心调度)**：对于外层PSO评估的每一个粒子（即每一条确定的无人机轨迹），使用贪心算法来依次规划3枚干扰弹的投放。这是该粒子适应度函数的计算过程。  
   * 初始化总覆盖时间 $\mathrm{TotalCoverage}$ 为 0，已调度投放集合 $\mathrm{ScheduledDeployments}$ 为空。  
   * **对于第 $k=1,2,3$ 枚干扰弹**：  
     * 沿着已确定的无人机轨迹，搜索最佳的下一次投放时间 $t_{drop,k}$。这里的“最佳”指的是，该次投放所产生的遮蔽时间区间与 $\mathrm{ScheduledDeployments}$ 中已有的区间合并后，能使 $\mathrm{TotalCoverage}$ 增加最多。  
     * 同时，必须满足约束条件 $t_{drop,k}\ge t_{drop,k-1}+1\ \mathrm{s}$（对于 $k>1$）。  
     * 这个寻找最佳单次投放的子问题可以通过一维搜索或一个轻量级的 PSO 来解决。  
     * 将新生成的投放加入 $\mathrm{ScheduledDeployments}$，并更新 $\mathrm{TotalCoverage}$。  
3. **适应度计算**：内层贪心调度完成后得到的最终 $\mathrm{TotalCoverage}$，即为外层PSO粒子（无人机轨迹）的适应度值。

这种混合结构能够高效地解决问题：PSO负责在高维、复杂的连续空间中寻找最优轨迹，而贪心算法则在给定轨迹下，快速地进行序列化决策。

### **3.3. 结果与策略说明 (result1.xlsx)**

算法收敛后，全局最优粒子将给出一套完整的策略，包括无人机的飞行参数和三枚干扰弹各自的投放与起爆信息。

**表3：问题3的最优3枚干扰弹策略 (result1.xlsx)**

| 投放序号 | 无人机速度 ($\mathrm{m/s}$) | 无人机方向 ($\mathrm{rad}$) | 投放时间 ($\mathrm{s}$) | 起爆延迟 ($\mathrm{s}$) | 投放点坐标 $(x, y, z)$ | 起爆点坐标 $(x, y, z)$ |
| :---- | :---- | :---- | :---- | :---- | :---- | :---- |
| 1 | (待计算) | (待计算) | (待计算) | (待计算) | (待计算) | (待计算) |
| 2 | (同上) | (同上) | (待计算) | (待计算) | (待计算) | (待计算) |
| 3 | (同上) | (同上) | (待计算) | (待计算) | (待计算) | (待计算) |

---

## **4. 多无人机协同单干扰弹策略（问题4）**

本节将问题规模扩展至多个智能体协同对抗单一威胁，重点研究协同增效的策略。

### **4.1. 构建集中式多智能体优化问题**

**问题描述**：FY1, FY2, FY3 各投放1枚干扰弹拦截 M1，目标是最大化三次投放所形成的总遮蔽时间（并集）。

**决策变量**：优化问题的搜索空间是三架无人机所有策略参数的组合。这是一个 9 维的决策向量：$[\,v_{U_1},\theta_{U_1},t_{delay,1},\ v_{U_2},\theta_{U_2},t_{delay,2},\ v_{U_3},\theta_{U_3},t_{delay,3}\,]$。

**目标函数**：最大化由三枚干扰弹产生的三个独立遮蔽时间区间的并集总长度。

将此问题视为一个单一的高维优化问题，可以让算法自主发现智能体之间的协同作用。例如，算法可能会找到一种策略，其中一架无人机采取了对其自身而言次优的路径，但其产生的烟幕恰好为另一架无人机的烟幕创造了条件，从而使整体遮蔽效果达到最优。这体现了“集体大于个体之和”的协同原则。

### **4.2. 基于高维协同PSO的求解**

粒子编码：每个粒子直接编码为完整的 9 维决策向量。  
适应度函数：粒子的适应度值为三枚干扰弹产生的总组合遮蔽时间。计算该值需要分别模拟三次投放，得到三个遮蔽时间区间，然后计算这三个区间的并集长度。  
实现细节：由于搜索空间维度增加，为确保算法能有效探索并收敛，通常需要比问题2中更大规模的粒子种群和更多的迭代次数。

### **4.3. 结果与策略说明 (result2.xlsx)**

PSO算法收敛后得到的最优粒子，即为三架无人机的最佳协同策略。

**表4：问题4的协同3无人机策略 (result2.xlsx)**

| 无人机编号 | 飞行速度 ($\mathrm{m/s}$) | 飞行方向 ($\mathrm{rad}$) | 起爆延迟 ($\mathrm{s}$) | 投放点坐标 $(x, y, z)$ | 起爆点坐标 $(x, y, z)$ |
| :---- | :---- | :---- | :---- | :---- | :---- |
| FY1 | (待计算) | (待计算) | (待计算) | (待计算) | (待计算) |
| FY2 | (待计算) | (待计算) | (待计算) | (待计算) | (待计算) |
| FY3 | (待计算) | (待计算) | (待计算) | (待计算) | (待计算) |

---

## **5. 综合多无人机、多导弹交战策略（问题5）**

本节旨在解决最终的、最复杂的顶层问题，这需要一个分层决策框架来有效应对其巨大的计算复杂度。

### **5.1. 分层求解框架：任务分配与协同优化**

**问题复杂性**：对全部 5 架无人机、3 枚导弹以及最多 15 枚干扰弹的所有可能策略进行一体化优化，其决策空间维度极高，计算上是不可行的。因此，必须对问题进行分解。

**两阶段方法**：

1. **阶段一：任务分配**。首先解决战略层面的问题：应该由哪些无人机去对抗哪些导弹？这是一个离散的分配问题。  
2. **阶段二：协同执行**。在任务分配确定后，原问题被分解为若干个规模更小、相互独立的子问题。然后针对每个子问题（例如，某几架无人机对抗某一枚导弹）进行战术层面的协同优化。

这种分层分解的思想是解决大规模多智能体系统问题的关键。它允许我们将一个复杂问题拆解为战略分配和战术执行两个层面，并为每个层面选择最适合的算法。对于离散的分配问题，可以使用组合优化算法；对于连续的轨迹规划问题，可以使用启发式优化算法。

### **5.2. 子问题一：基于匈牙利法的最优无人机-导弹分配**

**构建成本/效能矩阵**：

* 为了进行最优分配，需要一个量化指标来评估“一架无人机对抗一枚导弹的效能”。定义效能得分 $S_{ij}$ 为无人机 $U_i$ 使用单枚干扰弹对抗导弹 $M_j$ 所能实现的最大遮蔽时间。  
* 这个得分可以通过运行问题2中的PSO模型来计算。对每一个无人机-导弹对 $(U_i,M_j)$ 都进行一次优化计算，从而构建一个 $5\times 3$ 的效能矩阵。  
* 这个分配问题是一个典型的“指派问题”（Assignment Problem），其目标是在满足约束（每个无人机只能分配给一个目标）的情况下，最大化总效能。

**应用匈牙利算法**：

* 匈牙利法是解决指派问题的经典高效算法，其时间复杂度为 $O(n^3)$ 11。  
* 为了使用标准匈牙利法（通常用于最小化成本），可以将效能矩阵 $S$ 转化为成本矩阵 $C$，例如 $C_{ij}=S_{\max}-S_{ij}$，其中 $S_{\max}$ 是效能矩阵中的最大值。  
* 运行匈牙利算法 12，即可得到最大化总效能的无人机到导弹的最优分配方案。

### **5.3. 子问题二：多干扰弹投放的分布式优化**

**求解子问题**：匈牙利法给出了分配方案后（例如，$\{\mathrm{FY1},\mathrm{FY2}\}\to M1$，$\{\mathrm{FY3}\}\to M2$，$\{\mathrm{FY4},\mathrm{FY5}\}\to M3$），原问题分解为三个独立的子问题，每个子问题都可以用前几节开发的方法来解决。

* **单无人机任务组**：如果一个任务组只有一架无人机（如 FY3 vs M2），则使用第3节中的混合贪心-PSO算法来规划其多枚（最多3枚）干扰弹的投放。  
* **多无人机任务组**：如果一个任务组有多架无人机（如 $\{\mathrm{FY1},\mathrm{FY2}\}$ vs M1），则使用第4节中的高维协同PSO算法，并将其扩展以处理每架无人机投放多枚干扰弹的情况。

**干扰弹资源分配**：题目要求“至多投放3枚”。在优化过程中，需要决定每个任务组中的每架无人机具体使用多少枚干扰弹。这可以作为优化的一部分，或者采用一个简单的启发式规则，例如，让每架无人机都使用其最大载弹量。

### **5.4. 集成求解算法与最终策略说明 (result3.xlsx)**

**端到端完整算法流程**：

1. **构建效能矩阵**：对所有 15 个 $(\mathrm{UAV},\ \mathrm{Missile})$ 对，运行单枚干扰弹最优策略模型（问题 2 的 PSO），计算最大遮蔽时间，填充 $5\times 3$ 效能矩阵。  
2. **运行匈牙利法**：对效能矩阵求解指派问题，得到最优的无人机-导弹分组。  
3. **并行求解子问题**：对于每个分组，调用相应的多干扰弹/多无人机优化模型（问题3或问题4的扩展模型），计算详细的投放策略。  
4. **整合结果**：汇总所有子问题的解，形成最终的综合策略。

**表5：问题5的综合多智能体策略 (result3.xlsx)**

| 导弹目标 | 无人机编号 | 投放序号 | 飞行速度 ($\mathrm{m/s}$) | 飞行方向 ($\mathrm{rad}$) | 投放时间 ($\mathrm{s}$) | 起爆延迟 ($\mathrm{s}$) |
| :---- | :---- | :---- | :---- | :---- | :---- | :---- |
| M1 | FY1 | 1 | (待计算) | (待计算) | (待计算) | (待计算) |
| M1 | FY1 | 2 | (同上) | (同上) | (待计算) | (待计算) |
| M1 | FY2 | 1 | (待计算) | (待计算) | (待计算) | (待计算) |
| M2 | FY3 | 1 | (待计算) | (待计算) | (待计算) | (待计算) |
| ... | ... | ... | ... | ... | ... | ... |

---

## **6. 结论与模型扩展**

### **6.1. 最优策略总结与核心发现**

通过对五个场景的建模与求解，可以总结出一些具有普遍性的策略特征。例如，最优策略往往倾向于在更靠近被保护目标的位置形成烟幕屏障，而不是在靠近来袭导弹的位置。这是因为在远端形成的烟幕，随着导弹的高速接近，其遮蔽视线锥的有效时间窗口会非常短暂。此外，多无人机协同策略的核心在于形成一个时空上连续或有效衔接的“烟幕墙”，而非简单地将多个烟幕云团在同一区域重叠。

### **6.2. 模型局限性与未来改进方向**

本报告中建立的模型基于一系列理想化假设，这为问题的 tractable 求解提供了基础，但也带来了局限性。

**模型局限性**：

* **环境因素**：模型未考虑风场对烟幕云团飘移的影响，这在实际应用中是关键因素。  
* **空气动力学**：忽略了干扰弹下落过程中的空气阻力，这会影响弹道的精确性 1。  
* **烟幕模型**：将烟幕云团简化为均匀有效的硬球体，而实际烟幕的浓度呈高斯分布，且会随时间扩散衰减。  
* **信息完备性**：假设所有信息（位置、速度）都是精确且瞬时可得的，未考虑传感器误差和通信延迟。

**未来改进方向**：

* **引入风场模型**：在烟幕云团的运动学方程中加入风速向量，使其轨迹更为真实。  
* **修正弹道模型**：引入考虑空气阻力的弹道方程，提高干扰弹落点预测的准确性。  
* **精细化烟幕模型**：使用高斯烟羽模型等更复杂的流体力学模型来描述烟幕浓度分布和演化，将遮蔽判定条件从几何相交升级为基于积分光程的透过率阈值判断。  
* **鲁棒性与动态重规划**：引入不确定性（如导弹机动变轨），并开发能够应对突发状况的动态重规划算法，例如差分动态规划（Differential Dynamic Programming）等先进的轨迹优化技术 13。

#### **Works cited**

1. Projectile motion - Wikipedia, accessed September 4, 2025, https://en.wikipedia.org/wiki/Projectile_motion  
2. Chapter 3 Motion in Two and Three Dimensions, accessed September 4, 2025, https://www2.tntech.edu/leap/murdock/books/v1chap3.pdf  
3. Projectile Motion | Calculus III - Lumen Learning, accessed September 4, 2025, https://courses.lumenlearning.com/calculus3/chapter/projectile-motion/  
4. Cone-Sphere Intersection -- from Wolfram MathWorld, accessed September 4, 2025, https://mathworld.wolfram.com/Cone-SphereIntersection.html  
5. Intersection of a Cone and a Sphere - Wolfram Demonstrations Project, accessed September 4, 2025, https://demonstrations.wolfram.com/IntersectionOfAConeAndASphere/  
6. Intersection of a Sphere and a Cone - Geometric Tools, accessed September 4, 2025, https://www.geometrictools.com/Documentation/IntersectionSphereCone.pdf  
7. (PDF) UAV Path Planning Using an Adaptive Strategy for the ..., accessed September 4, 2025, https://www.researchgate.net/publication/389335697_UAV_Path_Planning_Using_an_Adaptive_Strategy_for_the_Particle_Swarm_Optimization_Algorithm  
8. 4.1 Interval Scheduling - TU Delft OpenCourseWare, accessed September 4, 2025, https://ocw.tudelft.nl/wp-content/uploads/Algoritmiek_Interval_scheduling.pdf  
9. Chapter 4 4.1 Interval Scheduling - Princeton CS, accessed September 4, 2025, https://www.cs.princeton.edu/~wayne/kleinberg-tardos/pearson/04GreedyAlgorithms-2x2.pdf  
10. Hungarian algorithm - Wikipedia, accessed September 4, 2025, https://en.wikipedia.org/wiki/Hungarian_algorithm  
11. Hungarian Algorithm for Assignment Problem (Introduction and ..., accessed September 4, 2025, https://www.geeksforgeeks.org/dsa/hungarian-algorithm-assignment-problem-set-1-introduction/  
12. Trajectory optimization - Wikipedia, accessed September 4, 2025, https://en.wikipedia.org/wiki/Trajectory_optimization
