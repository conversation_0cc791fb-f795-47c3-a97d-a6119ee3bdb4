"""
问题1：按给定固定策略计算 FY1 对 M1 的有效遮蔽时长。
- 坐标原点为假目标(0,0,0)
- 真目标：圆柱体，底心(0,200,0)，半径7，高10；几何中心(0,200,5)
- M1 初始(20000,0,2000)，恒速 300 m/s，方向指向原点
- FY1 初始(17800,0,1800)，接令后以 120 m/s 等高水平朝原点直线飞行
- t_drop=1.5s，t_det=1.5+3.6=5.1s，起爆后球半径R_C=10m，有效20s，球心下沉3m/s
- 遮蔽判据：锥-球几何，见 Isblock.md 摘要
"""

from math import sqrt, acos, asin, tan

# 允许不依赖第三方库，确保逻辑清晰与数值稳健

# 基本向量工具


def clamp(x: float, lo: float, hi: float) -> float:
    return lo if x < lo else hi if x > hi else x


def dot(a, b):
    return a[0] * b[0] + a[1] * b[1] + a[2] * b[2]


def norm(a):
    return sqrt(a[0] * a[0] + a[1] * a[1] + a[2] * a[2])


def add(a, b):
    return (a[0] + b[0], a[1] + b[1], a[2] + b[2])


def sub(a, b):
    return (a[0] - b[0], a[1] - b[1], a[2] - b[2])


def mul(a, k: float):
    return (a[0] * k, a[1] * k, a[2] * k)


def unit(a):
    n = norm(a)
    if n == 0:
        return (0.0, 0.0, 0.0)
    return (a[0] / n, a[1] / n, a[2] / n)


def cross(a, b):
    return (
        a[1] * b[2] - a[2] * b[1],
        a[2] * b[0] - a[0] * b[2],
        a[0] * b[1] - a[1] * b[0],
    )


# 常量参数
v_M = 300.0  # m/s
v_U = 120.0  # m/s
R_T = 7.0
H_T = 10.0
R_C = 10.0
T_eff = 20.0
v_sink = 3.0
g = 9.8

epsilon = 1e-9

# 固定时间点
t_drop = 1.5
t_det = 1.5 + 3.6

# 位置常量
O = (0.0, 0.0, 0.0)
P_T_bottom = (0.0, 200.0, 0.0)
P_T_top = (0.0, 200.0, 10.0)
P_T_center = (0.0, 200.0, 5.0)

P_M0 = (20000.0, 0.0, 2000.0)  # M1
P_U0 = (17800.0, 0.0, 1800.0)  # FY1

# FY1 等高水平朝原点飞行 => 水平方向 (-1,0,0)
U_dir = (-1.0, 0.0, 0.0)

# M1 朝原点匀速飞行
M_dir = unit(sub(O, P_M0))  # 指向原点的单位向量


def P_M(t: float):
    return add(P_M0, mul(M_dir, v_M * t))


def P_U(t: float):
    # 等高度直线（z 恒 1800）
    return add(P_U0, mul(U_dir, v_U * t))


def P_G(t: float):
    # 干扰弹在 t>=t_drop 后的抛体位置（投放瞬间水平速度=UAV速度，竖直初速=0）
    if t < t_drop:
        return P_U0
    P_d = P_U(t_drop)
    dt = t - t_drop
    v_hx = U_dir[0] * v_U
    v_hy = U_dir[1] * v_U
    x = P_d[0] + v_hx * dt
    y = P_d[1] + v_hy * dt
    z = P_d[2] - 0.5 * g * dt * dt
    return (x, y, z)


def P_C(t: float):
    # 烟幕球心（t>=t_det）
    P_det = P_G(t_det)
    if t <= t_det:
        return P_det
    return (P_det[0], P_det[1], P_det[2] - v_sink * (t - t_det))


def view_cone_axis(t: float):
    V = P_M(t)
    return unit(sub(P_T_center, V))


def view_cone_half_angle(t: float):
    # θ(t)=max(α_top+θ_top_subtend, α_bottom+θ_bottom_subtend)
    V = P_M(t)
    A = view_cone_axis(t)

    # 距离
    d_top = norm(sub(P_T_top, V))
    d_bot = norm(sub(P_T_bottom, V))

    # 角半径张角（arcsin）
    s_top = R_T / d_top if d_top > 0 else 1.0
    s_bot = R_T / d_bot if d_bot > 0 else 1.0
    th_top = asin(clamp(s_top, -1.0, 1.0))
    th_bot = asin(clamp(s_bot, -1.0, 1.0))

    # 轴线与连线的夹角（arccos）
    u_top_dir = unit(sub(P_T_top, V))
    u_bot_dir = unit(sub(P_T_bottom, V))
    c_top = clamp(dot(A, u_top_dir), -1.0, 1.0)
    c_bot = clamp(dot(A, u_bot_dir), -1.0, 1.0)
    a_top = acos(c_top)
    a_bot = acos(c_bot)

    return max(a_top + th_top, a_bot + th_bot)


def is_blocked(t: float) -> bool:
    # 仅在有效窗口内评估
    if t < t_det or t > t_det + T_eff:
        return False
    V = P_M(t)
    A = view_cone_axis(t)
    theta = view_cone_half_angle(t)
    C = P_C(t)

    L = sub(C, V)
    d_along = dot(L, A)
    if d_along < 0:
        return False  # 球在锥顶点后方

    # 到轴的垂距（叉乘范数 或 正交分解均可）
    # 使用更稳定的正交分解：
    L2 = dot(L, L)
    perp2 = max(L2 - d_along * d_along, 0.0)
    d_axis = sqrt(perp2)

    # 该处锥截面半径
    R_cone = d_along * tan(theta)

    return (d_axis + R_cone) <= (R_C + 1e-9)


def compute_effective遮蔽时长(dt: float = 0.01) -> float:
    # 数值积分统计 True 的总时长
    t0 = t_det
    t1 = t_det + T_eff
    t = t0
    total = 0.0
    while t <= t1 + 0.5 * dt:
        if is_blocked(t):
            total += dt
        t += dt
    return total


def sanity_check_points():
    P_d = P_U(t_drop)
    P_det = P_G(t_det)
    return P_d, P_det


if __name__ == "__main__":
    P_d, P_det = sanity_check_points()
    total = compute_effective遮蔽时长(0.01)
    # 输出关键结果（保留三位小数）
    print(f"P_drop={P_d}", flush=True)
    print(f"P_det={P_det}", flush=True)
    print(f"effective_masking_duration_seconds={total:.3f}", flush=True)
