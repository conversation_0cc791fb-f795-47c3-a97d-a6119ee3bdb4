from math import sqrt, acos, asin, tan


def limit(val, low, high):
    return low if val < low else high if val > high else val

def dot(u, v):
    return u[0] * v[0] + u[1] * v[1] + u[2] * v[2]

def length(vec):
    return sqrt(vec[0] * vec[0] + vec[1] * vec[1] + vec[2] * vec[2])

def vadd(u, v):
    return (u[0] + v[0], u[1] + v[1], u[2] + v[2])

def vsub(u, v):
    return (u[0] - v[0], u[1] - v[1], u[2] - v[2])

def scale(vec, k):
    return (vec[0] * k, vec[1] * k, vec[2] * k)

def normalize(vec):
    mag = length(vec)
    if mag == 0:
        return (0.0, 0.0, 0.0)
    return (vec[0] / mag, vec[1] / mag, vec[2] / mag)

def cross_product(u, v):
    return (
        u[1] * v[2] - u[2] * v[1],
        u[2] * v[0] - u[0] * v[2],
        u[0] * v[1] - u[1] * v[0],
    )


MISSILE_SPEED = 300.0
UAV_SPEED = 120.0
TARGET_RADIUS = 7.0
TARGET_HEIGHT = 10.0
SMOKE_RADIUS = 10.0
EFFECT_TIME = 20.0
SINK_RATE = 3.0
GRAVITY = 9.8

EPS = 1e-9

DROP_TIME = 1.5
DETONATE_TIME = 1.5 + 3.6

ORIGIN = (0.0, 0.0, 0.0)
TARGET_BASE = (0.0, 200.0, 0.0)
TARGET_TOP = (0.0, 200.0, 10.0)
TARGET_CENTER = (0.0, 200.0, 5.0)

MISSILE_START = (20000.0, 0.0, 2000.0)
UAV_START = (17800.0, 0.0, 1800.0)

UAV_DIRECTION = (-1.0, 0.0, 0.0)
MISSILE_DIRECTION = normalize(vsub(ORIGIN, MISSILE_START))


def missile_pos(t):
    return vadd(MISSILE_START, scale(MISSILE_DIRECTION, MISSILE_SPEED * t))

def uav_pos(t):
    return vadd(UAV_START, scale(UAV_DIRECTION, UAV_SPEED * t))

def grenade_pos(t):
    if t < DROP_TIME:
        return UAV_START
    drop_pos = uav_pos(DROP_TIME)
    dt = t - DROP_TIME
    vx = UAV_DIRECTION[0] * UAV_SPEED
    vy = UAV_DIRECTION[1] * UAV_SPEED
    x = drop_pos[0] + vx * dt
    y = drop_pos[1] + vy * dt
    z = drop_pos[2] - 0.5 * GRAVITY * dt * dt
    return (x, y, z)

def smoke_center(t):
    det_pos = grenade_pos(DETONATE_TIME)
    if t <= DETONATE_TIME:
        return det_pos
    return (det_pos[0], det_pos[1], det_pos[2] - SINK_RATE * (t - DETONATE_TIME))


def cone_axis(t):
    vertex = missile_pos(t)
    return normalize(vsub(TARGET_CENTER, vertex))

def cone_half_angle(t):
    vertex = missile_pos(t)
    axis = cone_axis(t)

    dist_top = length(vsub(TARGET_TOP, vertex))
    dist_bottom = length(vsub(TARGET_BASE, vertex))

    sin_top = TARGET_RADIUS / dist_top if dist_top > 0 else 1.0
    sin_bottom = TARGET_RADIUS / dist_bottom if dist_bottom > 0 else 1.0
    angle_top = asin(limit(sin_top, -1.0, 1.0))
    angle_bottom = asin(limit(sin_bottom, -1.0, 1.0))

    dir_top = normalize(vsub(TARGET_TOP, vertex))
    dir_bottom = normalize(vsub(TARGET_BASE, vertex))
    cos_top = limit(dot(axis, dir_top), -1.0, 1.0)
    cos_bottom = limit(dot(axis, dir_bottom), -1.0, 1.0)
    alpha_top = acos(cos_top)
    alpha_bottom = acos(cos_bottom)

    return max(alpha_top + angle_top, alpha_bottom + angle_bottom)


def check_blocking(t):
    if t < DETONATE_TIME or t > DETONATE_TIME + EFFECT_TIME:
        return False
    vertex = missile_pos(t)
    axis = cone_axis(t)
    theta = cone_half_angle(t)
    center = smoke_center(t)

    vec = vsub(center, vertex)
    along_dist = dot(vec, axis)
    if along_dist < 0:
        return False

    vec_sq = dot(vec, vec)
    perp_sq = max(vec_sq - along_dist * along_dist, 0.0)
    axis_dist = sqrt(perp_sq)

    cone_radius = along_dist * tan(theta)

    return (axis_dist + cone_radius) <= (SMOKE_RADIUS + 1e-9)

def compute_effective_time(dt=0.01):
    start_time = DETONATE_TIME
    end_time = DETONATE_TIME + EFFECT_TIME
    current_time = start_time
    total_time = 0.0
    while current_time <= end_time + 0.5 * dt:
        if check_blocking(current_time):
            total_time += dt
        current_time += dt
    return total_time

def get_key_positions():
    drop_pos = uav_pos(DROP_TIME)
    det_pos = grenade_pos(DETONATE_TIME)
    return drop_pos, det_pos


if __name__ == "__main__":
    drop_pos, det_pos = get_key_positions()
    duration = compute_effective_time(0.01)
    print(f"P_drop={drop_pos}", flush=True)
    print(f"P_det={det_pos}", flush=True)
    print(f"effective_masking_duration_seconds={duration:.3f}", flush=True)
