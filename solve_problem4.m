function solve_problem4_robust()
% SOLVE_PROBLEM4_ROBUST - (鲁棒性与效率优化版)
%   利用粒子群算法(PSO)求解三架无人机协同干扰问题
%
%   本版本进行了关键优化:
%   1. 预计算: 将固定不变的导弹轨迹在主函数中预先计算，避免在适应度
%      函数中上万次的重复计算，大幅提升运行效率。
%   2. 多次运行: 整个优化过程独立运行多次(例如5次)，并取其中最好的
%      结果，极大增强了找到全局最优解的稳定性和可靠性。
%   3. 参数增强: 适度增加了粒子群规模(SwarmSize)，以提高搜索多样性。

    %% 1. 定义问题常量 (Constants Definition)
    
    % 目标 (被保护的圆柱体)
    P_T_center = [0, 200, 5];   % 几何中心
    P_T_top    = [0, 200, 10];  % 顶部圆心
    P_T_bottom = [0, 200, 0];   % 底部圆心
    R_T = 7;                    % 半径 (m)

    % 威胁 (导弹 M1)
    P_M0 = [20000, 0, 2000];    % 初始位置 (m)
    v_M_scalar = 300;           % 速度大小 (m/s)
    P_Tgt = [0, 0, 0];          % 航向目标点
    
    % 干扰工具 (三架无人机 & 烟幕弹)
    P_UAV0_all = [17800,    0, 1800;  % FY1 初始位置
                  12000, 1400, 1400;  % FY2 初始位置
                   6000,-3000,  700]; % FY3 初始位置
    R_C = 10;                   % 烟幕球半径 (m)
    v_sink = 3;                 % 烟幕下沉速度 (m/s)
    T_smoke_effective = 20;     % 烟幕有效持续时间 (s)
    
    % 物理常量
    g = [0, 0, -9.8];           % 重力加速度 (m/s^2)

    %% 2. 仿真与优化参数设置 (Simulation and PSO Settings)
    
    % 仿真时间设置
    dist_M_to_Tgt = norm(P_Tgt - P_M0);
    T_max = dist_M_to_Tgt / v_M_scalar; % 导弹飞行总时间
    dt = 0.1;                           % 时间步长 (s)
    t_vec = (0:dt:T_max)';              % 时间向量 (N x 1)
    
    % 粒子群优化参数
    nvars = 12; % 变量数量
    lb_single = [0,   70,  0,  0];
    lb = repmat(lb_single, 1, 3); 
    ub_single = [2*pi, 140, T_max, T_max/2];
    ub = repmat(ub_single, 1, 3);

    %% 3. 预计算与多次独立运行 (Pre-calculation & Multiple Independent Runs)
    
    % --- 关键优化 1: 预计算不变的导弹轨迹 ---
    fprintf('正在预计算导弹轨迹 (仅执行一次)...\n');
    v_M_unit = (P_Tgt - P_M0) / norm(P_Tgt - P_M0);
    v_M_vec = v_M_scalar * v_M_unit;
    P_M_precalculated = P_M0 + t_vec * v_M_vec; % 预计算好的导弹轨迹矩阵
    
    % --- 关键优化 2: 设置多次独立运行 ---
    num_runs = 5; % <-- 可调整的独立运行次数
    best_solution_overall = [];
    max_time_overall = -inf;

    fprintf('\n开始执行 %d 次独立的优化过程...\n', num_runs);
    
    for run_idx = 1:num_runs
        fprintf('\n-------------------------------------------------\n');
        fprintf('--- 开始第 %d / %d 次独立优化 ---\n', run_idx, num_runs);
        fprintf('-------------------------------------------------\n');
        
        % 设置PSO选项
        fminconOptions = optimoptions('fmincon', 'Display', 'none', 'FunctionTolerance', 1e-7);
        options = optimoptions('particleswarm', ...
            'Display', 'iter', ...
            'SwarmSize', 800, ...      % <-- 已增加粒子数量
            'MaxIterations', 300, ...
            'PlotFcn', @pswplotbestf, ...
            'UseParallel', true, ...
            'HybridFcn', {@fmincon, fminconOptions});

        % 定义适应度函数句柄，将预计算的轨迹传入
        fitnessHandle = @(x) fitnessFunction(x, P_M_precalculated, P_UAV0_all, P_T_center, P_T_top, P_T_bottom, R_T, R_C, v_sink, T_smoke_effective, g, t_vec, dt);
        
        % 执行单次优化
        [solution_current, time_neg_current] = particleswarm(fitnessHandle, nvars, lb, ub, options);
        time_current = -time_neg_current;
        
        fprintf('--- 第 %d 次优化完成. 本次最优遮蔽时间: %.4f 秒 ---\n', run_idx, time_current);
        
        % 检查并更新全局最优解
        if time_current > max_time_overall
            fprintf('>>> 发现新的全局最优解! 从 %.4f 提升至 %.4f\n', max_time_overall, time_current);
            max_time_overall = time_current;
            best_solution_overall = solution_current;
        end
    end

    %% 4. 显示与保存最终结果 (Display and Save Final Results)
    
    fprintf('\n=================================================\n');
    fprintf('所有 %d 次独立优化全部完成。\n', num_runs);
    fprintf('最终确定的最大有效遮蔽时间: %.4f 秒\n', max_time_overall);
    fprintf('=================================================\n');
    
    display_and_save_results(best_solution_overall, max_time_overall, P_UAV0_all, g);
    
end


%% --- 适应度函数 (Fitness Function) ---
% 为了清晰，我们将其作为独立函数，而不是嵌套函数
function total_time_neg = fitnessFunction(x, P_M, P_UAV0_all, P_T_center, P_T_top, P_T_bottom, R_T, R_C, v_sink, T_smoke_effective, g, t_vec, dt)
    
    is_obscured_at_t = false(length(t_vec), 1);
    
    for i = 1:3 % 遍历三架无人机
        idx_base = (i - 1) * 4;
        phi = x(idx_base + 1);
        v_UAV = x(idx_base + 2);
        t_fly = x(idx_base + 3);
        t_delay = x(idx_base + 4);
        
        P_UAV0_i = P_UAV0_all(i, :);

        v_UAV_vec = v_UAV * [cos(phi), sin(phi), 0];
        t_det = t_fly + t_delay;
        P_drop = P_UAV0_i + t_fly * v_UAV_vec;
        P_det = P_drop + t_delay * v_UAV_vec + 0.5 * g * (t_delay^2);
        
        t_start = t_det;
        t_end = t_start + T_smoke_effective;
        
        active_indices = find(t_vec >= t_start & t_vec < t_end);
        if isempty(active_indices), continue; end
        
        t_active = t_vec(active_indices);
        P_M_active = P_M(active_indices, :);
        
        time_since_det = t_active - t_det;
        P_C_active = P_det + time_since_det * [0, 0, -v_sink];
        
        L_center = P_T_center - P_M_active;
        dist_M_to_center = vecnorm(L_center, 2, 2);
        L_top = P_T_top - P_M_active; L_bottom = P_T_bottom - P_M_active;
        dist_M_to_top = vecnorm(L_top, 2, 2); dist_M_to_bottom = vecnorm(L_bottom, 2, 2);
        
        % Clip arguments of acos to [-1, 1] to avoid NaN due to floating point errors
        cos_arg_top = max(-1, min(1, sum(L_top .* L_center, 2) ./ (dist_M_to_top .* dist_M_to_center)));
        cos_arg_bottom = max(-1, min(1, sum(L_bottom .* L_center, 2) ./ (dist_M_to_bottom .* dist_M_to_center)));
        alpha_top = acos(cos_arg_top);
        alpha_bottom = acos(cos_arg_bottom);
        
        theta_subtend_top = asin(R_T ./ dist_M_to_top);
        theta_subtend_bottom = asin(R_T ./ dist_M_to_bottom);
        cone_half_angle = max(alpha_top + theta_subtend_top, alpha_bottom + theta_subtend_bottom);
        
        L_smoke_to_missile = P_C_active - P_M_active;
        cone_axis_unit = L_center ./ dist_M_to_center;
        d_along = sum(L_smoke_to_missile .* cone_axis_unit, 2);
        d_axis_sq = vecnorm(L_smoke_to_missile, 2, 2).^2 - d_along.^2;
        d_axis = sqrt(max(0, d_axis_sq));
        R_cone = d_along .* tan(cone_half_angle);
        
        is_bomb_i_obscuring = (d_axis <= R_C - R_cone) & (d_along > 0);
        
        is_obscured_at_t(active_indices) = is_obscured_at_t(active_indices) | is_bomb_i_obscuring;
    end
    
    total_time = sum(is_obscured_at_t) * dt;
    total_time_neg = -total_time;
end

%% --- 结果展示与保存函数 ---
function display_and_save_results(s, max_obscuration_time, P_UAV0_all, g)
    if isempty(s)
        fprintf('未找到任何有效解。\n');
        return;
    end
    fprintf('--- 最终最优协同投放策略 ---\n');
    
    results_data = cell(3, 8);
    col_headers = {'UAV_ID', '飞行方向_deg', '飞行速度_ms', '投弹时刻_s', '投弹位置X_m', '投弹位置Y_m', '投弹位置Z_m', '引爆延迟_s'};
    
    for i = 1:3
        idx_base = (i - 1) * 4;
        phi = s(idx_base + 1);
        v_uav = s(idx_base + 2);
        t_fly = s(idx_base + 3);
        t_delay = s(idx_base + 4);
        
        P_UAV0_i = P_UAV0_all(i, :);
        v_uav_vec = v_uav * [cos(phi), sin(phi), 0];
        P_drop = P_UAV0_i + t_fly * v_uav_vec;
        
        fprintf('\n--- 无人机 FY%d 策略 ---\n', i);
        fprintf('  - 飞行方向 (度): %.4f\n', rad2deg(phi));
        fprintf('  - 飞行速度 (m/s): %.4f\n', v_uav);
        fprintf('  - 投弹时刻 (s): %.4f\n', t_fly);function solve_problem5_stage2()
% SOLVE_PROBLEM5_STAGE2 - 问题5阶段二：战术优化求解器
%
%   本脚本实现了针对问题5的分层递阶求解策略的第二阶段。
%   其核心功能是：对给定的战略分配方案，进行高效且精确的战术优化。
%
%   采用了“两阶段评估”策略：
%   1. 海选阶段: 使用粗糙参数，快速评估所有15个候选方案，筛选出优胜者。
%   2. 决赛阶段: 使用精细参数，对优胜方案进行高精度计算，得到最终结果。
%
    %% 1. 定义全局常量
    fprintf('--- 初始化问题常量 ---\n');
    % 导弹初始位置 M1, M2, M3
    P_M0_all = [20000,    0, 2000;
                19000,  600, 2100;
                18000, -600, 1900];
    % 无人机初始位置 FY1, FY2, FY3, FY4, FY5
    P_UAV0_all = [17800,    0, 1800;
                  12000, 1400, 1400;
                   6000,-3000,  700;
                  11000, 2000, 1800;
                  13000,-2000, 1300];
    % 其他常量
    CONSTS = struct(...
        'P_Tgt', [0, 0, 0], 'v_M_scalar', 300, ...
        'P_T_center', [0, 200, 5], 'P_T_top', [0, 200, 10], 'P_T_bottom', [0, 200, 0], ...
        'R_T', 7, 'R_C', 10, 'v_sink', 3, 'T_smoke_effective', 20, 'g', [0, 0, -9.8]);

    %% 2. 定义所有15个战略分配方案
    fprintf('--- 定义15个战略候选方案 ---\n');
    plans = define_strategic_plans();

    %% 3. 海选阶段 (Coarse Graining)
    fprintf('\n=================================================\n');
    fprintf('--- 开始海选阶段：快速评估所有 %d 个方案 ---\n', numel(plans));
    fprintf('=================================================\n');
    coarse_config = struct(...
        'dt', 0.2, ...
        'pso_iterations', 80, ...
        'pso_runs', 2, ...
        'use_hybrid', false); % 海选阶段不使用耗时的混合函数
    
    plan_scores_coarse = zeros(numel(plans), 1);
    
    % 使用并行for循环评估所有方案
    parfor i = 1:numel(plans)
        fprintf('海选评估中: %s\n', plans{i}.name);
        plan_scores_coarse(i) = evaluate_single_plan(plans{i}, P_M0_all, P_UAV0_all, CONSTS, coarse_config);
        fprintf('海选评估完成: %s, 得分: %.4f\n', plans{i}.name, plan_scores_coarse(i));
    end
    
    [~, sorted_indices] = sort(plan_scores_coarse, 'descend');
    fprintf('\n--- 海选阶段完成 ---\n');
    for i = 1:numel(plans)
        rank = find(sorted_indices == i);
        fprintf('排名 %d: %s, 粗评估分: %.4f\n', rank, plans{i}.name, plan_scores_coarse(i));
    end

    %% 4. 决赛阶段 (Fine Graining)
    num_finalists = 3; % 选择前3名进入决赛
    finalist_indices = sorted_indices(1:num_finalists);
    
    fprintf('\n=================================================\n');
    fprintf('--- 开始决赛阶段：精细评估排名前 %d 的方案 ---\n', num_finalists);
    fprintf('=================================================\n');
    fine_config = struct(...
        'dt', 0.05, ...
        'pso_iterations', 300, ...
        'pso_runs', 3, ...
        'use_hybrid', true); % 决赛阶段使用混合函数以求精确解

    final_results = {};
    for i = 1:num_finalists
        plan_idx = finalist_indices(i);
        plan = plans{plan_idx};
        fprintf('\n--- 决赛精细评估: %s ---\n', plan.name);
        
        [final_score, best_solution_vector] = evaluate_single_plan(plan, P_M0_all, P_UAV0_all, CONSTS, fine_config);
        
        final_results{i} = struct('plan', plan, 'score', final_score, 'solution', best_solution_vector);
        fprintf('--- 决赛评估完成: %s, 最终精确得分: %.4f ---\n', plan.name, final_score);
    end

    %% 5. 输出最终结果
    best_final_score = -inf;
    best_final_result = [];
    for i=1:numel(final_results)
        if final_results{i}.score > best_final_score
            best_final_score = final_results{i}.score;
            best_final_result = final_results{i};
        end
    end
    
    fprintf('\n*************************************************\n');
    fprintf('*** 最终优化结果                  ***\n');
    fprintf('*************************************************\n');
    fprintf('最优战略方案: %s\n', best_final_result.plan.name);
    fprintf('最终防御得分 (max(min(T_M1, T_M2, T_M3))): %.4f 秒\n', best_final_result.score);
    
    % 保存详细结果到Excel
    save_final_results_to_excel(best_final_result, P_UAV0_all, CONSTS);
end

%% --- Helper Function 1: 评估单个方案 ---
function [score, best_solution_vector] = evaluate_single_plan(plan, P_M0_all, P_UAV0_all, CONSTS, config)
    num_missiles = 3;
    subproblem_results = zeros(num_missiles, 1);
    subproblem_solutions = cell(num_missiles, 1);
    
    % 为三枚导弹创建并并行求解子问题
    parfor i = 1:num_missiles
        subproblem_def = struct(...
            'missile_idx', i, ...
            'uav_indices', plan.alloc{i});
        
        if isempty(subproblem_def.uav_indices)
            subproblem_results(i) = 0;
            subproblem_solutions{i} = [];
        else
            [subproblem_results(i), subproblem_solutions{i}] = ...
                solve_subproblem(subproblem_def, P_M0_all, P_UAV0_all, CONSTS, config);
        end
    end
    
    score = min(subproblem_results);
    % 将三个子问题的解向量拼接成一个大的总解向量
    best_solution_vector = horzcat(subproblem_solutions{:});
end

%% --- Helper Function 2: 求解单个子问题 ---
function [best_time, best_solution] = solve_subproblem(subproblem_def, P_M0_all, P_UAV0_all, CONSTS, config)
    num_uavs = numel(subproblem_def.uav_indices);
    nvars = num_uavs * 8; % 每个无人机8个决策变量 (问题3模型)
    
    % 预计算导弹轨迹
    missile_idx = subproblem_def.missile_idx;
    P_M0 = P_M0_all(missile_idx, :);
    dist = norm(CONSTS.P_Tgt - P_M0);
    T_max = dist / CONSTS.v_M_scalar;
    t_vec = (0:config.dt:T_max)';
    v_M_unit = (CONSTS.P_Tgt - P_M0) / dist;
    P_M = P_M0 + t_vec * (CONSTS.v_M_scalar * v_M_unit);

    % 自适应PSO参数
    swarm_size = max(80, 20 * nvars);
    
    % 设置变量界限
    lb_single = [0, 70, 0, 1, 1, 0, 0, 0];
    ub_single = [2*pi, 140, T_max/2, T_max/3, T_max/3, T_max/2, T_max/2, T_max/2];
    lb = repmat(lb_single, 1, num_uavs);
    ub = repmat(ub_single, 1, num_uavs);

    % 多次独立运行PSO
    best_time = -inf;
    best_solution = [];
    
    for run = 1:config.pso_runs
        % 定义适应度函数句柄
        fitnessHandle = @(x) fitness_function_subproblem(x, subproblem_def, P_M, P_UAV0_all, CONSTS, t_vec, config.dt);
        
        % 设置PSO选项
        options = optimoptions('particleswarm', 'Display', 'none', 'SwarmSize', swarm_size, ...
                               'MaxIterations', config.pso_iterations, 'UseParallel', false); % 内层PSO不使用并行
        if config.use_hybrid
            fminconOpts = optimoptions('fmincon', 'Display', 'none', 'FunctionTolerance', 1e-7);
            options.HybridFcn = {@fmincon, fminconOpts};
        end
        
        [sol_current, time_neg_current] = particleswarm(fitnessHandle, nvars, lb, ub, options);
        if -time_neg_current > best_time
            best_time = -time_neg_current;
            best_solution = sol_current;
        end
    end
end

%% --- Helper Function 3: 子问题适应度函数 ---
function total_time_neg = fitness_function_subproblem(x, subproblem_def, P_M, P_UAV0_all, CONSTS, t_vec, dt)
    is_obscured_at_t = false(length(t_vec), 1);
    num_uavs = numel(subproblem_def.uav_indices);

    for k = 1:num_uavs % 遍历这个小组里的所有无人机
        uav_master_idx = subproblem_def.uav_indices(k);
        P_UAV0_i = P_UAV0_all(uav_master_idx, :);
        
        % 解码当前无人机的8个决策变量
        x_k = x( (k-1)*8+1 : k*8 );
        phi = x_k(1); v_UAV = x_k(2); t_fly1 = x_k(3); dt_fly2 = x_k(4);
        dt_fly3 = x_k(5); t_del1 = x_k(6); t_del2 = x_k(7); t_del3 = x_k(8);
        
        % 计算该无人机3枚弹的起爆信息
        v_UAV_vec = v_UAV * [cos(phi), sin(phi), 0];
        t_drop = [t_fly1; t_fly1 + dt_fly2; t_fly1 + dt_fly2 + dt_fly3];
        t_det = t_drop + [t_del1; t_del2; t_del3];
        P_drop = P_UAV0_i + t_drop .* v_UAV_vec;
        delays_vec = [t_del1; t_del2; t_del3];
        P_det_k = P_drop + delays_vec .* v_UAV_vec + 0.5 * CONSTS.g .* (delays_vec.^2);

        % 对这3枚弹进行遮蔽判断
        for bomb_idx = 1:3
            t_start = t_det(bomb_idx);
            t_end = t_start + CONSTS.T_smoke_effective;
            P_det_current = P_det_k(bomb_idx, :);

            active_indices = find(t_vec >= t_start & t_vec < t_end);
            if isempty(active_indices), continue; end
            
            t_active = t_vec(active_indices);
            P_M_active = P_M(active_indices, :);
            time_since_det = t_active - t_start;
            P_C_active = P_det_current + time_since_det * [0, 0, -CONSTS.v_sink];
            
            % ... 此处为与之前完全相同的视锥遮蔽判断核心代码 ...
            L_center = CONSTS.P_T_center - P_M_active;
            dist_M_to_center = vecnorm(L_center, 2, 2);
            L_top = CONSTS.P_T_top - P_M_active; L_bottom = CONSTS.P_T_bottom - P_M_active;
            dist_M_to_top = vecnorm(L_top, 2, 2); dist_M_to_bottom = vecnorm(L_bottom, 2, 2);
            cos_arg_top = max(-1, min(1, sum(L_top .* L_center, 2) ./ (dist_M_to_top .* dist_M_to_center)));
            cos_arg_bottom = max(-1, min(1, sum(L_bottom .* L_center, 2) ./ (dist_M_to_bottom .* dist_M_to_center)));
            alpha_top = acos(cos_arg_top); alpha_bottom = acos(cos_arg_bottom);
            theta_subtend_top = asin(CONSTS.R_T ./ dist_M_to_top);
            theta_subtend_bottom = asin(CONSTS.R_T ./ dist_M_to_bottom);
            cone_half_angle = max(alpha_top + theta_subtend_top, alpha_bottom + theta_subtend_bottom);
            L_smoke_to_missile = P_C_active - P_M_active;
            cone_axis_unit = L_center ./ dist_M_to_center;
            d_along = sum(L_smoke_to_missile .* cone_axis_unit, 2);
            d_axis_sq = vecnorm(L_smoke_to_missile, 2, 2).^2 - d_along.^2;
            d_axis = sqrt(max(0, d_axis_sq));
            R_cone = d_along .* tan(cone_half_angle);
            is_bomb_obscuring = (d_axis <= CONSTS.R_C - R_cone) & (d_along > 0);
            is_obscured_at_t(active_indices) = is_obscured_at_t(active_indices) | is_bomb_obscuring;
        end
    end
    
    total_time = sum(is_obscured_at_t) * dt;
    total_time_neg = -total_time;
end

%% --- Helper Function 4 & 5: 定义方案与保存结果 ---
function plans = define_strategic_plans()
    plans = {};
    % Plan 1-9
    plans{1}  = struct('name', '方案1 (均衡防御)', 'alloc', {{[1], [4, 2], [5, 3]}});
    plans{2}  = struct('name', '方案2 (均衡-主将)', 'alloc', {{[1, 3], [4], [5, 2]}});
    plans{3}  = struct('name', '方案3 (极限补短板)', 'alloc', {{[1], [4], [5, 2, 3]}});
    plans{4}  = struct('name', '方案4 (尖刀对决-均衡)', 'alloc', {{[1, 2], [4], [5, 3]}});
    plans{5}  = struct('name', '方案5 (尖刀对决-集中)', 'alloc', {{[1], [4, 2, 3], [5]}});
    plans{6}  = struct('name', '方案6 (集中优势-中路)', 'alloc', {{[1, 4, 5], [2], [3]}});
    plans{7}  = struct('name', '方案7 (集中优势-右翼)', 'alloc', {{[3], [1, 2, 4], [5]}});
    plans{8}  = struct('name', '方案8 (区域负责)', 'alloc', {{[1], [2, 4], [5, 3]}});
    plans{9}  = struct('name', '方案9 (区域-增援中路)', 'alloc', {{[1, 2], [4], [5, 3]}});
    % Plan 10-15
    plans{10} = struct('name', '方案10(精英领队)', 'alloc', {{[1, 3], [4, 2], [5]}});
    plans{11} = struct('name', '方案11(双重保险)', 'alloc', {{[1, 5], [4, 2], [3]}});
    plans{12} = struct('name', '方案12(重点突破)', 'alloc', {{[1], [2, 4], [3, 5]}});
    plans{13} = struct('name', '方案13(全线压制)', 'alloc', {{[1, 2], [4], [5]}}); % FY3 as reserve
    plans{14} = struct('name', '方案14(三叉戟)', 'alloc', {{[1, 3], [4], [5]}});   % FY2 as reserve
    plans{15} = struct('name', '方案15(极限单防)', 'alloc', {{[1], [4], [5]}});     % FY2,3 as reserve
end

function save_final_results_to_excel(result, P_UAV0_all, CONSTS)
    plan = result.plan;
    sol_vec = result.solution;
    
    filename = 'result3.xlsx';
    
    % 创建一个大的cell数组来存储所有数据
    output_data = {'参数', '数值', '单位'};
    output_data = [output_data; {'最优战略方案', plan.name, ''}];
    output_data = [output_data; {'最终防御得分', result.score, '秒'}];
    output_data = [output_data; {'---', '---', '---'}];
    
    current_pos = 1;
    for i = 1:3 % M1, M2, M3
        uav_indices = plan.alloc{i};
        output_data = [output_data; {sprintf('对抗导弹 M%d 的无人机', i), '', ''}];
        if isempty(uav_indices)
            output_data = [output_data; {'(无)', '', ''}];
            continue;
        end
        
        for k = 1:numel(uav_indices)
            uav_idx = uav_indices(k);
            x_k = sol_vec(current_pos : current_pos+7);
            current_pos = current_pos + 8;
            
            phi = x_k(1); v_uav = x_k(2); t_fly1 = x_k(3); dt_fly2 = x_k(4);
            dt_fly3 = x_k(5); t_del1 = x_k(6); t_del2 = x_k(7); t_del3 = x_k(8);
            
            output_data = [output_data; {sprintf('  无人机 FY%d 策略:', uav_idx), '', ''}];
            output_data = [output_data; {'    飞行方向', rad2deg(phi), '度'}];
            output_data = [output_data; {'    飞行速度', v_uav, 'm/s'}];
            output_data = [output_data; {'    投弹1时刻', t_fly1, 's'}];
            output_data = [output_data; {'    投弹2时刻', t_fly1+dt_fly2, 's'}];
            output_data = [output_data; {'    投弹3时刻', t_fly1+dt_fly2+dt_fly3, 's'}];
            output_data = [output_data; {'    延迟1', t_del1, 's'}];
            output_data = [output_data; {'    延迟2', t_del2, 's'}];
            output_data = [output_data; {'    延迟3', t_del3, 's'}];
        end
    end
    
    try
        T = cell2table(output_data);
        writetable(T, filename, 'WriteVariableNames', false);
        fprintf('\n最终详细策略已成功保存到 %s\n', filename);
    catch ME
        fprintf('\n无法保存到Excel文件: %s\n', ME.message);
    end
end
        fprintf('  - 投弹位置 (x,y,z): (%.2f, %.2f, %.2f)\n', P_drop);
        fprintf('  - 引爆延迟 (s): %.4f\n', t_delay);

        results_data(i, :) = {sprintf('FY%d', i), rad2deg(phi), v_uav, t_fly, P_drop(1), P_drop(2), P_drop(3), t_delay};
    end
    
    try
        filename = 'result2.xlsx';
        T = cell2table(results_data, 'VariableNames', col_headers);
        
        summary_header = {'总有效遮蔽时间_s'};
        summary_data = {max_obscuration_time};
        T_summary = cell2table(summary_data, 'VariableNames', summary_header);
        
        writetable(T, filename, 'Sheet', 1, 'Range', 'A1');
        writetable(T_summary, filename, 'Sheet', 1, 'Range', 'J1');
        
        fprintf('\n最终结果已成功保存到 %s\n', filename);
    catch ME
        fprintf('\n无法保存到Excel文件: %s\n', ME.message);
    end
end