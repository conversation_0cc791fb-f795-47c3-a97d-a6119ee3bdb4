"""
美化版关键图例生成器 - 问题1核心图表
重新理解有效遮蔽概念，确保数据准确性，美化图表设计
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.patches as patches
from matplotlib.patches import Circle
import warnings
warnings.filterwarnings('ignore')

# 导入问题1的核心函数
from problem1 import *

# 设置现代化图表样式
import matplotlib
matplotlib.rcParams['font.family'] = ['DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 12
plt.rcParams['axes.linewidth'] = 1.2
plt.rcParams['lines.linewidth'] = 2.2
plt.rcParams['axes.grid'] = True
plt.rcParams['grid.alpha'] = 0.3
plt.rcParams['axes.axisbelow'] = True

# 定义美观的配色方案
COLORS = {
    'missile': '#E74C3C',      # 鲜红色 - 导弹
    'smoke': '#F39C12',        # 橙色 - 烟幕
    'target': '#8E44AD',       # 紫色 - 目标
    'uav': '#3498DB',          # 蓝色 - 无人机
    'cone': '#E74C3C',         # 红色 - 视锥
    'axis': '#C0392B',         # 深红色 - 轴线
    'blocking': '#27AE60',     # 绿色 - 遮蔽
    'background': '#ECF0F1',   # 浅灰色 - 背景
    'grid': '#BDC3C7'          # 灰色 - 网格
}

def create_enhanced_figures():
    """创建美化版关键图例 - 2x2布局"""
    
    # 创建更大的图形以提高清晰度
    fig = plt.figure(figsize=(16, 12), facecolor='white')
    fig.suptitle('Problem 1: Enhanced Smoke Grenade Deployment Analysis', 
                 fontsize=16, fontweight='bold', y=0.95, color='#2C3E50')
    
    # 图1：增强版几何模型原理图
    ax1 = plt.subplot(2, 2, 1)
    plot_enhanced_geometric_model(ax1)
    
    # 图2：美化版3D轨迹图
    ax2 = plt.subplot(2, 2, 2, projection='3d')
    plot_enhanced_3d_trajectories(ax2)
    
    # 图3：精确遮蔽效果分析
    ax3 = plt.subplot(2, 2, 3)
    plot_accurate_blocking_analysis(ax3)
    
    # 图4：美化版参数演化图
    ax4 = plt.subplot(2, 2, 4)
    plot_enhanced_parameters(ax4)
    
    plt.tight_layout(rect=[0, 0.03, 1, 0.93])
    return fig

def plot_enhanced_geometric_model(ax):
    """增强版几何模型原理图 - 展示有效遮蔽概念"""
    ax.set_xlim(-1.5, 8)
    ax.set_ylim(-3, 3)
    ax.set_facecolor('#FAFAFA')
    
    # 视锥参数 - 使用更真实的角度
    vertex = np.array([0, 0])
    cone_angle = 0.4  # 约23度，更接近实际导引头视场
    cone_length = 6.5
    
    # 绘制视锥填充区域
    upper_bound = vertex + cone_length * np.array([np.cos(cone_angle), np.sin(cone_angle)])
    lower_bound = vertex + cone_length * np.array([np.cos(-cone_angle), np.sin(-cone_angle)])
    
    # 视锥填充
    cone_x = [vertex[0], upper_bound[0], lower_bound[0], vertex[0]]
    cone_y = [vertex[1], upper_bound[1], lower_bound[1], vertex[1]]
    ax.fill(cone_x, cone_y, color=COLORS['cone'], alpha=0.15, label='Sight Cone')
    
    # 视锥边界
    ax.plot([vertex[0], upper_bound[0]], [vertex[1], upper_bound[1]], 
            color=COLORS['cone'], linewidth=3, label='Cone Boundary')
    ax.plot([vertex[0], lower_bound[0]], [vertex[1], lower_bound[1]], 
            color=COLORS['cone'], linewidth=3)
    ax.plot([vertex[0], cone_length], [vertex[1], 0], 
            color=COLORS['axis'], linewidth=2, linestyle='--', alpha=0.8, label='Cone Axis')
    
    # 烟幕球 - 有效遮蔽示例
    smoke_center = np.array([3.2, 0.2])
    smoke_radius = 1.0
    
    # 计算该位置的视锥半径和几何参数
    cone_radius = smoke_center[0] * np.tan(cone_angle)
    d_axis = abs(smoke_center[1])
    
    # 绘制烟幕球
    circle = Circle(smoke_center, smoke_radius, fill=True, 
                   facecolor=COLORS['smoke'], alpha=0.3, 
                   edgecolor=COLORS['smoke'], linewidth=3, label='Smoke Sphere')
    ax.add_patch(circle)
    
    # 目标点
    target = np.array([6.5, 0])
    ax.scatter(*target, color=COLORS['target'], s=200, marker='*', 
              label='Protected Target', zorder=5, edgecolors='white', linewidth=2)
    
    # 导弹标注
    ax.annotate('Missile', vertex, xytext=(-0.5, -1.0), fontsize=12, ha='center',
                arrowprops=dict(arrowstyle='->', color=COLORS['missile'], lw=2),
                bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
    
    # 关键几何量标注
    # d_axis标注
    ax.plot([smoke_center[0], smoke_center[0]], [0, smoke_center[1]], 
            'b-', alpha=0.8, linewidth=2)
    ax.annotate(f'$d_{{axis}}$ = {d_axis:.1f}', 
                [smoke_center[0] - 0.3, smoke_center[1]/2], 
                fontsize=11, ha='center', color='blue', weight='bold',
                bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8))
    
    # R_cone标注
    ax.plot([smoke_center[0], smoke_center[0]], [0, cone_radius], 
            'g-', alpha=0.8, linewidth=2)
    ax.annotate(f'$R_{{cone}}$ = {cone_radius:.1f}', 
                [smoke_center[0] + 0.4, cone_radius/2], 
                fontsize=11, ha='center', color='green', weight='bold',
                bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8))
    
    # R_C标注
    ax.annotate(f'$R_C$ = {smoke_radius:.1f}', 
                smoke_center + np.array([0.7, 0.7]), 
                fontsize=11, ha='center', color=COLORS['smoke'], weight='bold',
                bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8))
    
    # 遮蔽条件标注
    condition = d_axis + cone_radius <= smoke_radius
    condition_text = f'{d_axis:.1f} + {cone_radius:.1f} = {d_axis + cone_radius:.1f} ≤ {smoke_radius:.1f}'
    ax.text(3.2, -2.3, f'Blocking Condition: {condition_text}', 
            fontsize=10, ha='center', 
            color='green' if condition else 'red', weight='bold',
            bbox=dict(boxstyle="round,pad=0.3", 
                     facecolor='lightgreen' if condition else 'lightcoral', alpha=0.8))
    
    ax.set_xlabel('Distance (relative units)', fontsize=12, weight='bold')
    ax.set_ylabel('Distance (relative units)', fontsize=12, weight='bold')
    ax.set_title('(a) Enhanced Blocking Condition Analysis', fontsize=13, weight='bold', pad=20)
    ax.legend(loc='upper left', fontsize=10, framealpha=0.9)
    ax.grid(True, alpha=0.4, linestyle='-', linewidth=0.5)
    ax.set_aspect('equal')

def plot_enhanced_3d_trajectories(ax):
    """美化版3D轨迹图 - 使用真实数据"""
    ax.set_facecolor('#F8F9FA')
    
    # 使用真实的时间范围和数据
    t_range = np.linspace(0, 30, 150)
    missile_traj = np.array([P_M(t) for t in t_range])
    
    # 烟幕轨迹（仅在有效期内）
    t_smoke = t_range[(t_range >= t_det) & (t_range <= t_det + T_eff)]
    if len(t_smoke) > 0:
        smoke_traj = np.array([P_C(t) for t in t_smoke])
        ax.plot(smoke_traj[:, 0], smoke_traj[:, 1], smoke_traj[:, 2], 
                color=COLORS['smoke'], linewidth=4, alpha=0.8, label='Smoke Trajectory')
    
    # 导弹轨迹 - 分段显示
    ax.plot(missile_traj[:, 0], missile_traj[:, 1], missile_traj[:, 2], 
            color=COLORS['missile'], linewidth=3, alpha=0.8, label='Missile Trajectory')
    
    # 关键点标记
    ax.scatter(*P_M0, color=COLORS['missile'], s=120, marker='^', 
              label='M1 Start', edgecolors='white', linewidth=2, zorder=5)
    ax.scatter(*P_U0, color=COLORS['uav'], s=120, marker='s', 
              label='FY1 Start', edgecolors='white', linewidth=2, zorder=5)
    ax.scatter(*P_T_center, color=COLORS['target'], s=180, marker='*', 
              label='Target Center', edgecolors='white', linewidth=2, zorder=5)
    
    # 绘制目标圆柱体
    theta = np.linspace(0, 2*np.pi, 32)
    x_cyl = P_T_bottom[0] + R_T * np.cos(theta)
    y_cyl = P_T_bottom[1] + R_T * np.sin(theta)
    z_bottom = np.full_like(x_cyl, P_T_bottom[2])
    z_top = np.full_like(x_cyl, P_T_bottom[2] + H_T)
    
    ax.plot(x_cyl, y_cyl, z_bottom, color=COLORS['target'], alpha=0.8, linewidth=2)
    ax.plot(x_cyl, y_cyl, z_top, color=COLORS['target'], alpha=0.8, linewidth=2)
    
    # 添加真实数据标注
    ax.text2D(0.02, 0.98, f'Drop Point: ({P_U0[0]:.0f}, {P_U0[1]:.0f}, {P_U0[2]:.0f})', 
              transform=ax.transAxes, fontsize=10, verticalalignment='top',
              bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
    ax.text2D(0.02, 0.92, f'Detonation: t = {t_det:.1f}s', 
              transform=ax.transAxes, fontsize=10, verticalalignment='top',
              bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
    
    ax.set_xlabel('X (m)', fontsize=12, weight='bold')
    ax.set_ylabel('Y (m)', fontsize=12, weight='bold')
    ax.set_zlabel('Z (m)', fontsize=12, weight='bold')
    ax.set_title('(b) Enhanced 3D Trajectory Analysis', fontsize=13, weight='bold', pad=20)
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=9, framealpha=0.9)
    ax.view_init(elev=20, azim=45)
    ax.grid(True, alpha=0.3)

def plot_accurate_blocking_analysis(ax):
    """精确遮蔽效果分析 - 使用真实计算数据"""
    # 使用更高精度的时间采样
    t_range = np.linspace(t_det, t_det + T_eff, 2000)
    blocking_status = [is_blocked(t) for t in t_range]
    
    # 绘制遮蔽状态
    ax.fill_between(t_range, 0, blocking_status, alpha=0.6, color=COLORS['blocking'], 
                    label='Effective Blocking Period')
    ax.plot(t_range, blocking_status, color='darkgreen', linewidth=2.5)
    
    # 计算精确数值
    dt = t_range[1] - t_range[0]
    total_time = sum(blocking_status) * dt
    efficiency = total_time / T_eff * 100
    
    # 添加精确数值标注
    textstr = f'Total Blocking Time: {total_time:.3f}s\nBlocking Efficiency: {efficiency:.2f}%\nWindow Duration: {T_eff:.1f}s'
    props = dict(boxstyle='round', facecolor='wheat', alpha=0.9, edgecolor='orange')
    ax.text(0.05, 0.95, textstr, transform=ax.transAxes, fontsize=11,
            verticalalignment='top', bbox=props, weight='bold')
    
    # 时间标记
    ax.axvline(x=t_det, color=COLORS['missile'], linestyle='--', alpha=0.8, 
               linewidth=2, label='Detonation Start')
    ax.axvline(x=t_det + T_eff, color=COLORS['missile'], linestyle='--', alpha=0.8, 
               linewidth=2, label='Effect Expiration')
    
    ax.set_xlabel('Time (s)', fontsize=12, weight='bold')
    ax.set_ylabel('Blocking Status', fontsize=12, weight='bold')
    ax.set_title('(c) Accurate Blocking Effectiveness Analysis', fontsize=13, weight='bold', pad=20)
    ax.set_ylim(-0.05, 1.05)
    ax.legend(fontsize=10, framealpha=0.9)
    ax.grid(True, alpha=0.4)

def plot_enhanced_parameters(ax):
    """美化版参数演化图 - 显示真实几何参数"""
    t_range = np.linspace(t_det, t_det + T_eff, 200)
    
    d_axis_vals = []
    r_cone_vals = []
    sum_vals = []
    
    # 计算真实的几何参数
    for t in t_range:
        V = P_M(t)
        A = view_cone_axis(t)
        theta = view_cone_half_angle(t)
        C = P_C(t)
        
        L = sub(C, V)
        d_along = dot(L, A)
        
        if d_along > 0:
            L2 = dot(L, L)
            perp2 = max(L2 - d_along * d_along, 0.0)
            d_axis = sqrt(perp2)
            R_cone = d_along * tan(theta)
            sum_val = d_axis + R_cone
        else:
            d_axis = np.nan
            R_cone = 0
            sum_val = np.nan
            
        d_axis_vals.append(d_axis)
        r_cone_vals.append(R_cone)
        sum_vals.append(sum_val)
    
    # 绘制参数曲线
    ax.plot(t_range, d_axis_vals, color='#3498DB', linewidth=3, label='$d_{axis}(t)$')
    ax.plot(t_range, r_cone_vals, color='#E74C3C', linewidth=3, label='$R_{cone}(t)$')
    ax.plot(t_range, sum_vals, color='#9B59B6', linewidth=3, label='$d_{axis} + R_{cone}$')
    ax.axhline(y=R_C, color=COLORS['smoke'], linestyle='--', linewidth=3, 
               label=f'$R_C$ = {R_C}m (Smoke Radius)')
    
    # 遮蔽条件区域
    valid_mask = ~np.isnan(sum_vals)
    blocked_mask = np.array(sum_vals) <= R_C
    combined_mask = valid_mask & blocked_mask
    
    if np.any(combined_mask):
        ax.fill_between(t_range, 0, 15, where=combined_mask, alpha=0.2, 
                        color=COLORS['blocking'], label='Blocking Condition Satisfied')
    
    ax.set_xlabel('Time (s)', fontsize=12, weight='bold')
    ax.set_ylabel('Distance (m)', fontsize=12, weight='bold')
    ax.set_title('(d) Enhanced Parameter Evolution', fontsize=13, weight='bold', pad=20)
    ax.legend(fontsize=10, framealpha=0.9)
    ax.grid(True, alpha=0.4)
    ax.set_ylim(0, 15)

if __name__ == "__main__":
    print("Generating enhanced figures for Problem 1...")
    
    # 生成美化版图表
    fig = create_enhanced_figures()
    fig.savefig('problem1_enhanced_analysis.png', dpi=300, bbox_inches='tight', facecolor='white')
    fig.savefig('problem1_enhanced_analysis.pdf', bbox_inches='tight', facecolor='white')
    
    # 生成数值报告
    print("\n" + "="*60)
    print("ENHANCED PROBLEM 1 ANALYSIS REPORT")
    print("="*60)
    
    # 获取真实计算数据
    P_d, P_det = sanity_check_points()
    total_time = compute_effective遮蔽时长(0.001)  # 更高精度
    
    print(f"Drop Point: ({P_d[0]:.1f}, {P_d[1]:.1f}, {P_d[2]:.1f}) m")
    print(f"Detonation Point: ({P_det[0]:.1f}, {P_det[1]:.1f}, {P_det[2]:.1f}) m")
    print(f"Smoke Sphere Radius: {R_C} m")
    print(f"Effective Window: {t_det:.1f}s - {t_det + T_eff:.1f}s ({T_eff}s total)")
    print(f"Total Effective Blocking Time: {total_time:.4f} seconds")
    print(f"Blocking Efficiency: {total_time/T_eff*100:.2f}%")
    print("="*60)
    
    print("\nGenerated enhanced figure:")
    print("- problem1_enhanced_analysis.png/pdf - Enhanced analysis with accurate data")
    
    plt.show()
