\subsubsection{问题2的建模准备}

问题2要求在问题1的基础上，通过优化无人机FY1的飞行参数和投放策略，最大化单枚烟幕干扰弹对导弹M1的有效遮蔽时长。这是一个典型的非线性约束优化问题，需要在连续的决策空间中寻找全局最优解。

\paragraph{决策变量的确定}

与问题1的固定策略不同，问题2将以下参数设为决策变量：

\begin{itemize}
    \item \textbf{无人机飞行速度} $v_U \in [70, 140]$ (m/s)：影响投放点位置和干扰弹初始动量
    \item \textbf{飞行方向角} $\theta \in [0, 2\pi)$ (rad)：决定无人机的飞行方向
    \item \textbf{投放时间} $t_{drop} \geq 0$ (s)：影响投放点的空间位置
    \item \textbf{引爆延迟} $t_{delay} \geq 0$ (s)：控制烟幕云团的起爆时机
\end{itemize}

其中起爆时间为 $t_{det} = t_{drop} + t_{delay}$。

\paragraph{约束条件分析}

基于物理可行性和战术合理性，建立如下约束条件：

\begin{align}
70 &\leq v_U \leq 140 \quad \text{(无人机速度限制)} \\
0 &\leq \theta < 2\pi \quad \text{(方向角范围)} \\
0 &\leq t_{drop} \leq 70 \quad \text{(考虑导弹到达时间约67s)} \\
0 &\leq t_{delay} \leq 20 \quad \text{(基于抛体运动时间约19.2s)}
\end{align}

\paragraph{目标函数构建}

目标函数为有效遮蔽时长的最大化：
\begin{equation}
\max_{v_U, \theta, t_{drop}, t_{delay}} T_{obs}(v_U, \theta, t_{drop}, t_{delay})
\end{equation}

其中 $T_{obs}$ 通过数值积分计算：
\begin{equation}
T_{obs} = \int_{t_{det}}^{t_{det}+20} \text{IsBlocked}(t; v_U, \theta, t_{drop}, t_{delay}) \, dt
\end{equation}

\subsubsection{问题2的模型建立}

\paragraph{运动学模型扩展}

在问题1固定参数模型的基础上，建立参数化的运动学模型：

\textbf{无人机轨迹}：
\begin{equation}
\mathbf{P}_U(t) = \mathbf{P}_{U0} + v_U \cdot t \cdot (\cos\theta, \sin\theta, 0)
\end{equation}

\textbf{干扰弹抛体运动}：
投放后的干扰弹遵循抛体运动规律，其位置为：
\begin{equation}
\mathbf{P}_G(t) = \begin{cases}
\mathbf{P}_{U0} & \text{if } t < t_{drop} \\
\mathbf{P}_U(t_{drop}) + \mathbf{v}_{initial} \cdot (t - t_{drop}) + \frac{1}{2}\mathbf{g}(t - t_{drop})^2 & \text{if } t \geq t_{drop}
\end{cases}
\end{equation}

其中初始速度 $\mathbf{v}_{initial} = v_U(\cos\theta, \sin\theta, 0)$，重力加速度 $\mathbf{g} = (0, 0, -9.8)$。

\textbf{烟幕云团运动}：
起爆后的烟幕云团中心位置为：
\begin{equation}
\mathbf{P}_C(t) = \begin{cases}
\mathbf{P}_G(t_{det}) & \text{if } t \leq t_{det} \\
\mathbf{P}_G(t_{det}) + (0, 0, -v_{sink}(t - t_{det})) & \text{if } t > t_{det}
\end{cases}
\end{equation}

\paragraph{几何遮蔽模型}

沿用问题1的锥-球相交几何模型，但所有几何参数现在都依赖于决策变量：

\textbf{视锥参数计算}：
\begin{align}
\mathbf{V}(t) &= \mathbf{P}_M(t) \quad \text{(视锥顶点)} \\
\mathbf{A}(t) &= \frac{\mathbf{P}_{T_{center}} - \mathbf{V}(t)}{|\mathbf{P}_{T_{center}} - \mathbf{V}(t)|} \quad \text{(视锥轴线)} \\
\theta_{cone}(t) &= \max(\alpha_{top}(t) + \theta_{top}(t), \alpha_{bottom}(t) + \theta_{bottom}(t)) \quad \text{(视锥半角)}
\end{align}

\textbf{遮蔽判定条件}：
对于时刻 $t \in [t_{det}, t_{det} + 20]$，遮蔽条件为：
\begin{equation}
\text{IsBlocked}(t) = \begin{cases}
1 & \text{if } d_{axis}(t) + R_{cone}(t) \leq R_C \text{ and } d_{along}(t) > 0 \\
0 & \text{otherwise}
\end{cases}
\end{equation}

其中：
\begin{align}
\mathbf{L}(t) &= \mathbf{P}_C(t) - \mathbf{V}(t) \\
d_{along}(t) &= \mathbf{L}(t) \cdot \mathbf{A}(t) \\
d_{axis}(t) &= \sqrt{|\mathbf{L}(t)|^2 - d_{along}(t)^2} \\
R_{cone}(t) &= d_{along}(t) \cdot \tan(\theta_{cone}(t))
\end{align}

\paragraph{优化模型数学表述}

综合上述分析，问题2的完整数学模型为：

\begin{align}
\max \quad & T_{obs} = \sum_{t \in \mathcal{T}} \text{IsBlocked}(t; v_U, \theta, t_{drop}, t_{delay}) \cdot \Delta t \\
\text{s.t.} \quad & 70 \leq v_U \leq 140 \\
& 0 \leq \theta < 2\pi \\
& 0 \leq t_{drop} \leq 70 \\
& 0 \leq t_{delay} \leq 20 \\
& t_{det} = t_{drop} + t_{delay} \\
& \mathcal{T} = \{t_{det} + k\Delta t : k = 0, 1, \ldots, \lfloor 20/\Delta t \rfloor\}
\end{align}

其中 $\Delta t = 0.01$s 为数值积分步长。

\subsubsection{问题2的模型求解}

\paragraph{算法选择与设计}

由于目标函数 $T_{obs}$ 具有以下特点：
\begin{itemize}
    \item \textbf{非线性}：涉及复杂的几何计算和三角函数
    \item \textbf{非凸性}：可能存在多个局部最优解
    \item \textbf{不可导}：IsBlocked函数为离散布尔函数
    \item \textbf{计算复杂}：每次函数评估需要数值积分
\end{itemize}

选择粒子群优化算法(PSO)作为求解方法，其优势在于：
\begin{itemize}
    \item 无需梯度信息，适用于黑盒优化
    \item 全局搜索能力强，能够跳出局部最优
    \item 参数设置简单，收敛性能稳定
\end{itemize}

\paragraph{PSO算法实现}

\textbf{粒子表示}：每个粒子 $\mathbf{x}_i = (v_U^i, \theta^i, t_{drop}^i, t_{delay}^i)$ 代表一个完整的投放策略。

\textbf{适应度函数}：
\begin{equation}
f(\mathbf{x}_i) = \text{effective\_mask\_time}(\mathbf{P}_{M0}, \mathbf{P}_{U0}, v_U^i, \theta^i, t_{drop}^i, t_{det}^i)
\end{equation}

\textbf{速度更新公式}：
\begin{equation}
\mathbf{v}_i^{t+1} = w\mathbf{v}_i^t + c_1r_1(\mathbf{p}_i - \mathbf{x}_i^t) + c_2r_2(\mathbf{g} - \mathbf{x}_i^t)
\end{equation}

\textbf{位置更新公式}：
\begin{equation}
\mathbf{x}_i^{t+1} = \mathbf{x}_i^t + \mathbf{v}_i^{t+1}
\end{equation}

\textbf{边界处理}：
\begin{align}
v_U^i &= \text{clamp}(v_U^i, 70, 140) \\
\theta^i &= \theta^i \bmod 2\pi \\
t_{drop}^i &= \text{clamp}(t_{drop}^i, 0, 70) \\
t_{delay}^i &= \text{clamp}(t_{delay}^i, 0, 20)
\end{align}

\paragraph{算法参数设置}

基于PSO理论和实验调优，采用以下参数配置：
\begin{align}
\text{种群规模} &: N = 100 \\
\text{最大迭代次数} &: T_{max} = 200 \\
\text{惯性权重} &: w = 0.729 \\
\text{学习因子} &: c_1 = c_2 = 1.49
\end{align}

\paragraph{数值求解流程}

\begin{enumerate}
    \item \textbf{初始化}：在约束范围内随机生成100个粒子的初始位置和速度
    \item \textbf{适应度评估}：计算每个粒子的有效遮蔽时长
    \item \textbf{更新个体最优}：更新每个粒子的历史最佳位置 $\mathbf{p}_i$
    \item \textbf{更新全局最优}：更新全局最佳位置 $\mathbf{g}$
    \item \textbf{速度和位置更新}：根据PSO公式更新粒子状态
    \item \textbf{边界约束处理}：确保所有粒子满足约束条件
    \item \textbf{收敛判断}：达到最大迭代次数或收敛精度要求时停止
\end{enumerate}

\paragraph{计算复杂度分析}

\textbf{时间复杂度}：$O(N \times T_{max} \times T_{eval})$
\begin{itemize}
    \item $N = 100$：种群规模
    \item $T_{max} = 200$：最大迭代次数  
    \item $T_{eval} = O(2000)$：单次适应度评估的时间复杂度（2000个时间步）
\end{itemize}

总计算复杂度约为 $O(4 \times 10^7)$ 次基本运算。

\paragraph{优化求解过程分析}

PSO算法的收敛过程表明了优化问题的复杂性：
\begin{itemize}
    \item \textbf{初期探索阶段}（第1-15代）：适应度函数值为0，表明随机初始化的粒子未能找到有效的遮蔽策略
    \item \textbf{突破阶段}（第16-20代）：算法发现可行解，适应度从0快速提升至4.46秒
    \item \textbf{精细优化阶段}（第21-200代）：通过局部搜索逐步改进，最终收敛至4.51秒
\end{itemize}

收敛曲线显示算法在第134代达到最优值4.51秒，随后保持稳定，表明找到了全局最优解。

\paragraph{最优策略求解结果}

经过200代PSO优化，获得问题2的最优投放策略如下：

\textbf{决策变量最优值}：
\begin{align}
v_U^* &= 70.236 \text{ m/s} \\
\theta^* &= 0.069829 \text{ rad} = 4.00° \\
t_{drop}^* &= 1.728 \text{ s} \\
t_{delay}^* &= 0.000 \text{ s}
\end{align}

\textbf{最优策略解释}：

\begin{enumerate}
    \item \textbf{无人机飞行参数}：
    \begin{itemize}
        \item \textbf{飞行速度}：70.236 m/s，接近速度下限，表明较低速度有利于精确投放
        \item \textbf{飞行方向}：4.00°，几乎沿x轴负方向飞行，略微偏向y轴正方向
        \item \textbf{飞行方向向量}：$\mathbf{d} = (\cos(4.00°), \sin(4.00°), 0) = (0.9976, 0.0698, 0)$
    \end{itemize}

    \item \textbf{投放策略参数}：
    \begin{itemize}
        \item \textbf{投放时间}：1.728秒，相比问题1的固定值1.5秒略有延后
        \item \textbf{引爆延迟}：0.000秒，表明最优策略是投放后立即起爆
        \item \textbf{起爆时间}：$t_{det}^* = 1.728 + 0.000 = 1.728$秒
    \end{itemize}
\end{enumerate}

\textbf{关键位置坐标}：

根据运动学方程计算得到：
\begin{align}
\mathbf{P}_{drop}^* &= (17921.09, 8.47, 1800.0) \text{ m} \\
\mathbf{P}_{det}^* &= (17921.09, 8.47, 1800.0) \text{ m}
\end{align}

由于引爆延迟为0，投放点与起爆点重合，表明干扰弹在投放瞬间即起爆形成烟幕云团。

\textbf{性能提升分析}：

最优策略实现的有效遮蔽时长为4.51秒，相比问题1的固定策略（1.34秒）提升了：
\begin{equation}
\text{提升率} = \frac{4.51 - 1.34}{1.34} \times 100\% = 236.6\%
\end{equation}

这一显著提升验证了优化策略的有效性。

\paragraph{结果物理合理性分析}

\begin{enumerate}
    \item \textbf{速度选择合理性}：选择接近下限的速度(70.236 m/s)有利于：
    \begin{itemize}
        \item 延长无人机在目标区域的停留时间
        \item 提高投放精度，减少位置误差
        \item 降低干扰弹的初始动能，有利于精确定位
    \end{itemize}

    \item \textbf{方向角选择合理性}：4.00°的小角度偏转表明：
    \begin{itemize}
        \item 主要沿x轴负方向接近目标，符合战术要求
        \item 轻微的y方向分量可能有助于优化遮蔽几何关系
        \item 避免了大幅度机动，保持飞行稳定性
    \end{itemize}

    \item \textbf{时间参数选择合理性}：
    \begin{itemize}
        \item 投放时间1.728秒确保在导弹威胁到达前完成部署
        \item 零延迟起爆最大化了烟幕的有效作用时间
        \item 起爆点位置(17921.09, 8.47, 1800.0)处于导弹与目标之间的理想拦截位置
    \end{itemize}
\end{enumerate}

\paragraph{求解结果验证}

通过以下方式验证求解结果的正确性：
\begin{enumerate}
    \item \textbf{约束满足性检查}：所有决策变量均满足约束条件
    \item \textbf{物理合理性分析}：投放点、起爆点的空间位置符合战术逻辑
    \item \textbf{收敛性验证}：算法在第134代后保持稳定，表明收敛可靠
    \item \textbf{性能提升验证}：相比固定策略实现236.6\%的性能提升
\end{enumerate}

该求解方法成功处理了问题2的非线性、非凸优化特性，为烟幕干扰弹的最优投放策略提供了可靠的数值解，显著提升了干扰效果。
