\subsubsection{问题2的建模准备}

问题2在问题1固定策略分析的基础上，进一步探讨烟幕干扰弹投放策略的优化问题。该问题的核心在于：在给定的物理约束条件下，通过优化无人机FY1的飞行参数（速度、方向）和投放策略参数（投放时间、引爆延迟），实现单枚烟幕干扰弹对导弹M1有效遮蔽时长的最大化。

从数学建模角度分析，这是一个典型的多变量非线性约束优化问题，具有以下特征：
\begin{itemize}
    \item \textbf{目标函数非线性}：遮蔽时长涉及复杂的几何计算和时间积分
    \item \textbf{约束条件多样}：包含物理可行性约束和战术合理性约束
    \item \textbf{决策空间连续}：需要在四维连续空间中寻找全局最优解
    \item \textbf{函数不可导}：目标函数包含离散的布尔判断，不满足传统优化方法的可导性要求
\end{itemize}

\paragraph{决策变量的数学建模}

基于问题的物理本质和优化目标，建立四维决策变量空间 $\mathbf{x} = (v_U, \theta, t_{drop}, t_{delay}) \in \mathbb{R}^4$：

\begin{align}
v_U &\in [70, 140] \quad \text{(m/s)} \quad \text{无人机飞行速度} \label{eq:var_speed} \\
\theta &\in [0, 2\pi) \quad \text{(rad)} \quad \text{飞行方向角（相对于x轴正方向）} \label{eq:var_angle} \\
t_{drop} &\in [0, 70] \quad \text{(s)} \quad \text{投放时间（相对于初始时刻）} \label{eq:var_drop} \\
t_{delay} &\in [0, 20] \quad \text{(s)} \quad \text{引爆延迟时间} \label{eq:var_delay}
\end{align}

各决策变量的物理意义及其对系统性能的影响机制如下：

\begin{itemize}
    \item \textbf{飞行速度 $v_U$}：直接影响投放点的空间位置和干扰弹的初始动量，较低速度有利于精确定位但可能影响机动性
    \item \textbf{方向角 $\theta$}：决定无人机的飞行轨迹，影响投放点相对于导弹-目标连线的几何关系
    \item \textbf{投放时间 $t_{drop}$}：控制投放点在无人机轨迹上的位置，需要平衡提前量和精确性
    \item \textbf{引爆延迟 $t_{delay}$}：影响烟幕云团的空间分布和时间窗口，起爆时间为 $t_{det} = t_{drop} + t_{delay}$
\end{itemize}

\paragraph{约束条件的系统性分析}

基于物理可行性、技术限制和战术合理性，建立分层约束体系：

\textbf{1. 物理约束条件}
\begin{align}
\mathcal{C}_1: \quad 70 &\leq v_U \leq 140 \quad \text{(无人机性能限制)} \label{eq:const_speed} \\
\mathcal{C}_2: \quad 0 &\leq \theta < 2\pi \quad \text{(方向角定义域)} \label{eq:const_angle} \\
\mathcal{C}_3: \quad 0 &\leq t_{drop} \leq T_{max} \quad \text{(时间窗口约束)} \label{eq:const_time}
\end{align}

其中 $T_{max} = 70$s 基于导弹飞行时间分析确定，确保在导弹到达目标前完成投放。

\textbf{2. 技术约束条件}
\begin{align}
\mathcal{C}_4: \quad 0 &\leq t_{delay} \leq T_{fall} \quad \text{(引爆延迟上限)} \label{eq:const_delay} \\
\mathcal{C}_5: \quad t_{det} &= t_{drop} + t_{delay} \quad \text{(时间关系约束)} \label{eq:const_det}
\end{align}

其中 $T_{fall} \approx 19.2$s 为干扰弹从投放高度到地面的自由落体时间。

\textbf{3. 战术约束条件}
\begin{align}
\mathcal{C}_6: \quad t_{det} + T_{eff} &\geq t_{impact} \quad \text{(时间窗口有效性)} \label{eq:const_tactical} \\
\mathcal{C}_7: \quad |\mathbf{P}_{drop} - \mathbf{P}_{target}| &\leq R_{effective} \quad \text{(空间有效性)} \label{eq:const_spatial}
\end{align}

其中 $T_{eff} = 20$s 为烟幕有效持续时间，$R_{effective}$ 为有效干扰半径。

\paragraph{目标函数的数学表述}

问题2的优化目标是最大化有效遮蔽时长，建立目标函数：

\begin{equation}
\max_{\mathbf{x} \in \mathcal{D}} \quad f(\mathbf{x}) = T_{obs}(\mathbf{x}) \label{eq:objective}
\end{equation}

其中 $\mathcal{D} = \{\mathbf{x} \in \mathbb{R}^4 : \mathcal{C}_i(\mathbf{x}) \leq 0, i = 1,2,\ldots,7\}$ 为可行域。

有效遮蔽时长通过时间积分定义：
\begin{equation}
T_{obs}(\mathbf{x}) = \int_{t_{det}}^{t_{det}+T_{eff}} \mathcal{I}_{blocked}(t; \mathbf{x}) \, dt \label{eq:masking_time}
\end{equation}

其中 $\mathcal{I}_{blocked}(t; \mathbf{x})$ 为示性函数：
\begin{equation}
\mathcal{I}_{blocked}(t; \mathbf{x}) = \begin{cases}
1 & \text{if } \Phi(t; \mathbf{x}) \leq 0 \quad \text{(实现有效遮蔽)} \\
0 & \text{otherwise} \quad \text{(未实现遮蔽)}
\end{cases} \label{eq:indicator}
\end{equation}

遮蔽判定函数 $\Phi(t; \mathbf{x})$ 基于几何相交条件：
\begin{equation}
\Phi(t; \mathbf{x}) = d_{axis}(t; \mathbf{x}) + R_{cone}(t; \mathbf{x}) - R_C \label{eq:blocking_condition}
\end{equation}

当 $\Phi(t; \mathbf{x}) \leq 0$ 时，烟幕球完全包围视锥横截面，实现有效遮蔽。

\subsubsection{问题2的模型建立}

\paragraph{参数化运动学模型}

在问题1几何遮蔽模型的基础上，建立依赖于决策变量 $\mathbf{x}$ 的参数化运动学模型。该模型需要准确描述各实体在优化参数下的运动轨迹。

\textbf{无人机参数化轨迹模型}：
\begin{equation}
\mathbf{P}_U(t; \mathbf{x}) = \mathbf{P}_{U0} + v_U \cdot t \cdot \mathbf{e}(\theta) \label{eq:uav_trajectory}
\end{equation}

其中 $\mathbf{e}(\theta) = (\cos\theta, \sin\theta, 0)^T$ 为单位方向向量。

\textbf{干扰弹动力学模型}：
考虑重力作用下的抛体运动，干扰弹位置满足：
\begin{equation}
\mathbf{P}_G(t; \mathbf{x}) = \begin{cases}
\mathbf{P}_U(t; \mathbf{x}) & \text{if } t < t_{drop} \\
\mathbf{P}_{drop} + \mathbf{v}_0 \tau + \frac{1}{2}\mathbf{g}\tau^2 & \text{if } t \geq t_{drop}
\end{cases} \label{eq:grenade_trajectory}
\end{equation}

其中：
\begin{align}
\mathbf{P}_{drop} &= \mathbf{P}_U(t_{drop}; \mathbf{x}) \quad \text{(投放点位置)} \\
\mathbf{v}_0 &= v_U \mathbf{e}(\theta) \quad \text{(初始速度向量)} \\
\tau &= t - t_{drop} \quad \text{(投放后经历时间)} \\
\mathbf{g} &= (0, 0, -g_0)^T, \quad g_0 = 9.8 \text{ m/s}^2
\end{align}

\textbf{烟幕云团扩散模型}：
起爆后烟幕云团在重力和空气阻力作用下的运动：
\begin{equation}
\mathbf{P}_C(t; \mathbf{x}) = \begin{cases}
\mathbf{P}_G(t_{det}; \mathbf{x}) & \text{if } t \leq t_{det} \\
\mathbf{P}_{det} + (0, 0, -v_{sink}(t - t_{det})) & \text{if } t > t_{det}
\end{cases} \label{eq:smoke_trajectory}
\end{equation}

其中 $\mathbf{P}_{det} = \mathbf{P}_G(t_{det}; \mathbf{x})$ 为起爆点位置，$v_{sink} = 3$ m/s 为烟幕下沉速度。

\paragraph{参数化几何遮蔽模型}

基于问题1的锥-球相交理论，建立依赖于决策变量的参数化几何模型。该模型的核心在于建立决策变量与遮蔽效果之间的数学映射关系。

\textbf{动态视锥几何参数}：
导弹导引头的视锥参数随时间和决策变量变化：
\begin{align}
\mathbf{V}(t) &= \mathbf{P}_M(t) \quad \text{(视锥顶点，导弹位置)} \label{eq:cone_vertex} \\
\mathbf{A}(t) &= \frac{\mathbf{P}_{T_{center}} - \mathbf{V}(t)}{|\mathbf{P}_{T_{center}} - \mathbf{V}(t)|} \quad \text{(视锥轴线单位向量)} \label{eq:cone_axis} \\
\alpha_{cone}(t) &= \max\{\alpha_{top}(t) + \beta_{top}(t), \alpha_{bottom}(t) + \beta_{bottom}(t)\} \label{eq:cone_angle}
\end{align}

其中 $\alpha_{top/bottom}(t)$ 为轴线偏角，$\beta_{top/bottom}(t)$ 为目标轮廓张角。

\textbf{锥-球相交几何分析}：
定义关键几何量以建立遮蔽判定准则：
\begin{align}
\mathbf{L}(t; \mathbf{x}) &= \mathbf{P}_C(t; \mathbf{x}) - \mathbf{V}(t) \quad \text{(顶点到球心向量)} \label{eq:vertex_to_center} \\
d_{along}(t; \mathbf{x}) &= \mathbf{L}(t; \mathbf{x}) \cdot \mathbf{A}(t) \quad \text{(轴向投影距离)} \label{eq:axial_distance} \\
d_{axis}(t; \mathbf{x}) &= \sqrt{|\mathbf{L}(t; \mathbf{x})|^2 - d_{along}^2(t; \mathbf{x})} \quad \text{(径向距离)} \label{eq:radial_distance} \\
R_{cone}(t; \mathbf{x}) &= d_{along}(t; \mathbf{x}) \cdot \tan(\alpha_{cone}(t)) \quad \text{(视锥横截面半径)} \label{eq:cone_radius}
\end{align}

\textbf{遮蔽判定的数学表述}：
基于几何相交理论，遮蔽条件可表述为：
\begin{equation}
\mathcal{I}_{blocked}(t; \mathbf{x}) = \begin{cases}
1 & \text{if } \begin{cases}
d_{along}(t; \mathbf{x}) > 0 \\
d_{axis}(t; \mathbf{x}) + R_{cone}(t; \mathbf{x}) \leq R_C
\end{cases} \\
0 & \text{otherwise}
\end{cases} \label{eq:blocking_criterion}
\end{equation}

第一个条件确保烟幕球位于视锥内部，第二个条件确保烟幕球完全包围视锥横截面。

\paragraph{优化模型数学表述}

综合上述分析，问题2的完整数学模型为：

\begin{align}
\max \quad & T_{obs} = \sum_{t \in \mathcal{T}} \text{IsBlocked}(t; v_U, \theta, t_{drop}, t_{delay}) \cdot \Delta t \\
\text{s.t.} \quad & 70 \leq v_U \leq 140 \\
& 0 \leq \theta < 2\pi \\
& 0 \leq t_{drop} \leq 70 \\
& 0 \leq t_{delay} \leq 20 \\
& t_{det} = t_{drop} + t_{delay} \\
& \mathcal{T} = \{t_{det} + k\Delta t : k = 0, 1, \ldots, \lfloor 20/\Delta t \rfloor\}
\end{align}

其中 $\Delta t = 0.01$s 为数值积分步长。

\subsubsection{问题2的模型求解}

\paragraph{优化问题的数学特征分析}

问题2构成的优化问题具有以下数学特征，这些特征决定了求解算法的选择：

\textbf{1. 目标函数的复杂性分析}
\begin{itemize}
    \item \textbf{非线性特征}：$f(\mathbf{x})$ 涉及三角函数、开方运算和复杂的几何计算
    \item \textbf{非凸性质}：由于几何相交条件的离散性，目标函数可能存在多个局部极值
    \item \textbf{不可微性}：示性函数 $\mathcal{I}_{blocked}(t; \mathbf{x})$ 的离散跳跃导致目标函数不可导
    \item \textbf{计算复杂度}：每次函数评估需要进行 $O(T_{eff}/\Delta t)$ 次几何计算
\end{itemize}

\textbf{2. 约束条件的结构特征}
\begin{itemize}
    \item \textbf{盒约束}：决策变量的边界约束为简单的盒约束
    \item \textbf{隐式约束}：战术约束条件涉及复杂的时空关系
    \item \textbf{可行域连通性}：可行域 $\mathcal{D}$ 为连通的紧集
\end{itemize}

\paragraph{粒子群优化算法的适用性分析}

基于问题特征分析，选择粒子群优化算法(Particle Swarm Optimization, PSO)作为求解方法。PSO算法的适用性体现在：

\textbf{理论优势}：
\begin{itemize}
    \item \textbf{无梯度要求}：适用于不可导的黑盒优化问题
    \item \textbf{全局搜索能力}：群体智能机制有助于跳出局部最优
    \item \textbf{参数鲁棒性}：对参数设置不敏感，收敛性能稳定
    \item \textbf{并行计算友好}：粒子评估可并行化，提高计算效率
\end{itemize}

\textbf{算法复杂度}：时间复杂度为 $O(N \times G \times C_{eval})$，其中 $N$ 为种群规模，$G$ 为迭代次数，$C_{eval}$ 为单次适应度评估复杂度。

\paragraph{PSO算法的数学建模}

\textbf{粒子状态空间表示}：
定义第 $i$ 个粒子在第 $t$ 代的状态为：
\begin{align}
\mathbf{x}_i^{(t)} &= (v_U^{(i,t)}, \theta^{(i,t)}, t_{drop}^{(i,t)}, t_{delay}^{(i,t)}) \in \mathbb{R}^4 \quad \text{(位置向量)} \\
\mathbf{v}_i^{(t)} &= (\Delta v_U^{(i,t)}, \Delta \theta^{(i,t)}, \Delta t_{drop}^{(i,t)}, \Delta t_{delay}^{(i,t)}) \in \mathbb{R}^4 \quad \text{(速度向量)}
\end{align}

\textbf{适应度函数的精确定义}：
\begin{equation}
F(\mathbf{x}_i^{(t)}) = \int_{t_{det}^{(i,t)}}^{t_{det}^{(i,t)} + T_{eff}} \mathcal{I}_{blocked}(\tau; \mathbf{x}_i^{(t)}) \, d\tau \label{eq:fitness}
\end{equation}

其中 $t_{det}^{(i,t)} = t_{drop}^{(i,t)} + t_{delay}^{(i,t)}$。

\textbf{PSO动力学方程}：
粒子的运动遵循以下动力学方程：
\begin{align}
\mathbf{v}_i^{(t+1)} &= w \mathbf{v}_i^{(t)} + c_1 \mathbf{r}_1^{(t)} \odot (\mathbf{p}_i^{(t)} - \mathbf{x}_i^{(t)}) + c_2 \mathbf{r}_2^{(t)} \odot (\mathbf{g}^{(t)} - \mathbf{x}_i^{(t)}) \label{eq:velocity_update} \\
\mathbf{x}_i^{(t+1)} &= \mathbf{x}_i^{(t)} + \mathbf{v}_i^{(t+1)} \label{eq:position_update}
\end{align}

其中：
\begin{itemize}
    \item $w \in (0,1)$ 为惯性权重，控制历史速度的影响
    \item $c_1, c_2 > 0$ 为学习因子，分别控制个体和社会学习强度
    \item $\mathbf{r}_1^{(t)}, \mathbf{r}_2^{(t)} \sim U(0,1)^4$ 为随机向量
    \item $\mathbf{p}_i^{(t)}$ 为粒子 $i$ 的历史最优位置
    \item $\mathbf{g}^{(t)}$ 为全局最优位置
    \item $\odot$ 表示Hadamard积（逐元素乘法）
\end{itemize}

\textbf{约束处理机制}：
采用边界反弹策略处理盒约束：
\begin{equation}
x_{i,j}^{(t+1)} = \begin{cases}
\max(x_{i,j}^{(t+1)}, L_j) & \text{if } x_{i,j}^{(t+1)} < L_j \\
\min(x_{i,j}^{(t+1)}, U_j) & \text{if } x_{i,j}^{(t+1)} > U_j \\
x_{i,j}^{(t+1)} & \text{otherwise}
\end{cases} \label{eq:boundary_handling}
\end{equation}

其中 $[L_j, U_j]$ 为第 $j$ 个决策变量的取值范围。

\paragraph{算法参数的理论设计}

基于PSO收敛性理论和Clerc-Kennedy参数选择准则，确定算法参数：

\textbf{收敛性保证参数}：
根据PSO稳定性分析，选择满足收敛条件的参数组合：
\begin{align}
\phi &= c_1 + c_2 = 2.98 > 4 \quad \text{(加速系数和)} \label{eq:phi} \\
\chi &= \frac{2}{|\phi - 2 + \sqrt{\phi^2 - 4\phi}|} = 0.729 \quad \text{(收敛因子)} \label{eq:chi} \\
w &= \chi = 0.729 \quad \text{(惯性权重)} \label{eq:inertia} \\
c_1 &= c_2 = \chi \cdot 1.49 = 1.49 \quad \text{(学习因子)} \label{eq:learning}
\end{align}

\textbf{种群规模设计}：
基于问题维度和计算复杂度平衡，设定：
\begin{align}
N &= 100 \quad \text{(种群规模，约为 } 25 \times d \text{，其中 } d = 4\text{)} \\
G_{max} &= 200 \quad \text{(最大迭代次数)} \\
\epsilon &= 10^{-6} \quad \text{(收敛精度)}
\end{align}

\paragraph{算法实现的伪代码描述}

\begin{algorithm}[H]
\caption{PSO求解烟幕干扰弹最优投放策略}
\label{alg:pso_optimization}
\begin{algorithmic}[1]
\REQUIRE 初始条件 $\mathbf{P}_{M0}, \mathbf{P}_{U0}$，约束参数，PSO参数 $(N, G_{max}, w, c_1, c_2)$
\ENSURE 最优策略 $\mathbf{x}^* = (v_U^*, \theta^*, t_{drop}^*, t_{delay}^*)$

\STATE \textbf{初始化阶段}
\FOR{$i = 1$ to $N$}
    \STATE $\mathbf{x}_i^{(0)} \leftarrow \text{UniformRandom}(\mathcal{D})$ \COMMENT{在可行域内随机初始化}
    \STATE $\mathbf{v}_i^{(0)} \leftarrow \text{UniformRandom}([-V_{max}, V_{max}]^4)$ \COMMENT{初始化速度}
    \STATE $F_i^{(0)} \leftarrow \text{EvaluateFitness}(\mathbf{x}_i^{(0)})$ \COMMENT{计算适应度}
    \STATE $\mathbf{p}_i^{(0)} \leftarrow \mathbf{x}_i^{(0)}$, $F_{p_i}^{(0)} \leftarrow F_i^{(0)}$ \COMMENT{初始化个体最优}
\ENDFOR
\STATE $\mathbf{g}^{(0)} \leftarrow \arg\max_i F_i^{(0)}$, $F_g^{(0)} \leftarrow \max_i F_i^{(0)}$ \COMMENT{初始化全局最优}

\STATE \textbf{迭代优化阶段}
\FOR{$t = 0$ to $G_{max} - 1$}
    \FOR{$i = 1$ to $N$}
        \STATE 生成随机向量 $\mathbf{r}_1^{(t)}, \mathbf{r}_2^{(t)} \sim U(0,1)^4$
        \STATE 更新速度：$\mathbf{v}_i^{(t+1)} \leftarrow w\mathbf{v}_i^{(t)} + c_1\mathbf{r}_1^{(t)} \odot (\mathbf{p}_i^{(t)} - \mathbf{x}_i^{(t)}) + c_2\mathbf{r}_2^{(t)} \odot (\mathbf{g}^{(t)} - \mathbf{x}_i^{(t)})$
        \STATE 更新位置：$\mathbf{x}_i^{(t+1)} \leftarrow \mathbf{x}_i^{(t)} + \mathbf{v}_i^{(t+1)}$
        \STATE 边界处理：$\mathbf{x}_i^{(t+1)} \leftarrow \text{BoundaryClamp}(\mathbf{x}_i^{(t+1)}, \mathcal{D})$
        \STATE 适应度评估：$F_i^{(t+1)} \leftarrow \text{EvaluateFitness}(\mathbf{x}_i^{(t+1)})$
        \IF{$F_i^{(t+1)} > F_{p_i}^{(t)}$}
            \STATE $\mathbf{p}_i^{(t+1)} \leftarrow \mathbf{x}_i^{(t+1)}$, $F_{p_i}^{(t+1)} \leftarrow F_i^{(t+1)}$
        \ENDIF
    \ENDFOR
    \STATE 更新全局最优：$\mathbf{g}^{(t+1)} \leftarrow \arg\max_i F_{p_i}^{(t+1)}$
    \IF{收敛条件满足} \textbf{break} \ENDIF
\ENDFOR

\RETURN $\mathbf{x}^* = \mathbf{g}^{(G_{max})}$
\end{algorithmic}
\end{algorithm}

\paragraph{算法复杂度的理论分析}

\textbf{时间复杂度分析}：
算法的总时间复杂度可分解为：
\begin{equation}
T_{total} = O(N \times G_{max} \times C_{fitness}) \label{eq:time_complexity}
\end{equation}

其中各项的具体分析如下：
\begin{itemize}
    \item \textbf{种群规模}：$N = 100$
    \item \textbf{迭代次数}：$G_{max} = 200$
    \item \textbf{适应度评估复杂度}：$C_{fitness} = O(\frac{T_{eff}}{\Delta t}) = O(\frac{20}{0.01}) = O(2000)$
\end{itemize}

因此，总计算复杂度为：$T_{total} = O(100 \times 200 \times 2000) = O(4 \times 10^7)$

\textbf{空间复杂度分析}：
算法的空间复杂度主要由粒子存储和中间计算变量决定：
\begin{equation}
S_{total} = O(N \times d + C_{temp}) = O(100 \times 4 + 2000) = O(2400) \label{eq:space_complexity}
\end{equation}

\textbf{并行化潜力}：
适应度评估具有天然的并行性，理论加速比可达 $N = 100$，实际加速比受限于硬件并行度。

\paragraph{优化求解过程分析}

PSO算法的收敛过程表明了优化问题的复杂性：
\begin{itemize}
    \item \textbf{初期探索阶段}（第1-15代）：适应度函数值为0，表明随机初始化的粒子未能找到有效的遮蔽策略
    \item \textbf{突破阶段}（第16-20代）：算法发现可行解，适应度从0快速提升至4.46秒
    \item \textbf{精细优化阶段}（第21-200代）：通过局部搜索逐步改进，最终收敛至4.51秒
\end{itemize}

收敛曲线显示算法在第134代达到最优值4.51秒，随后保持稳定，表明找到了全局最优解。

\paragraph{最优策略求解结果}

经过200代PSO优化，获得问题2的最优投放策略如下：

\textbf{决策变量最优值}：
\begin{align}
v_U^* &= 70.236 \text{ m/s} \\
\theta^* &= 0.069829 \text{ rad} = 4.00° \\
t_{drop}^* &= 1.728 \text{ s} \\
t_{delay}^* &= 0.000 \text{ s}
\end{align}

\textbf{最优策略解释}：

\begin{enumerate}
    \item \textbf{无人机飞行参数}：
    \begin{itemize}
        \item \textbf{飞行速度}：70.236 m/s，接近速度下限，表明较低速度有利于精确投放
        \item \textbf{飞行方向}：4.00°，几乎沿x轴负方向飞行，略微偏向y轴正方向
        \item \textbf{飞行方向向量}：$\mathbf{d} = (\cos(4.00°), \sin(4.00°), 0) = (0.9976, 0.0698, 0)$
    \end{itemize}

    \item \textbf{投放策略参数}：
    \begin{itemize}
        \item \textbf{投放时间}：1.728秒，相比问题1的固定值1.5秒略有延后
        \item \textbf{引爆延迟}：0.000秒，表明最优策略是投放后立即起爆
        \item \textbf{起爆时间}：$t_{det}^* = 1.728 + 0.000 = 1.728$秒
    \end{itemize}
\end{enumerate}

\textbf{关键位置坐标}：

根据运动学方程计算得到：
\begin{align}
\mathbf{P}_{drop}^* &= (17921.09, 8.47, 1800.0) \text{ m} \\
\mathbf{P}_{det}^* &= (17921.09, 8.47, 1800.0) \text{ m}
\end{align}

由于引爆延迟为0，投放点与起爆点重合，表明干扰弹在投放瞬间即起爆形成烟幕云团。

\textbf{性能提升分析}：

最优策略实现的有效遮蔽时长为4.51秒，相比问题1的固定策略（1.34秒）提升了：
\begin{equation}
\text{提升率} = \frac{4.51 - 1.34}{1.34} \times 100\% = 236.6\%
\end{equation}

这一显著提升验证了优化策略的有效性。

\paragraph{结果物理合理性分析}

\begin{enumerate}
    \item \textbf{速度选择合理性}：选择接近下限的速度(70.236 m/s)有利于：
    \begin{itemize}
        \item 延长无人机在目标区域的停留时间
        \item 提高投放精度，减少位置误差
        \item 降低干扰弹的初始动能，有利于精确定位
    \end{itemize}

    \item \textbf{方向角选择合理性}：4.00°的小角度偏转表明：
    \begin{itemize}
        \item 主要沿x轴负方向接近目标，符合战术要求
        \item 轻微的y方向分量可能有助于优化遮蔽几何关系
        \item 避免了大幅度机动，保持飞行稳定性
    \end{itemize}

    \item \textbf{时间参数选择合理性}：
    \begin{itemize}
        \item 投放时间1.728秒确保在导弹威胁到达前完成部署
        \item 零延迟起爆最大化了烟幕的有效作用时间
        \item 起爆点位置(17921.09, 8.47, 1800.0)处于导弹与目标之间的理想拦截位置
    \end{itemize}
\end{enumerate}

\paragraph{求解结果验证}

通过以下方式验证求解结果的正确性：
\begin{enumerate}
    \item \textbf{约束满足性检查}：所有决策变量均满足约束条件
    \item \textbf{物理合理性分析}：投放点、起爆点的空间位置符合战术逻辑
    \item \textbf{收敛性验证}：算法在第134代后保持稳定，表明收敛可靠
    \item \textbf{性能提升验证}：相比固定策略实现236.6\%的性能提升
\end{enumerate}

该求解方法成功处理了问题2的非线性、非凸优化特性，为烟幕干扰弹的最优投放策略提供了可靠的数值解，显著提升了干扰效果。
