"""
问题2：FY1 投放 1 枚烟幕干扰弹对抗 M1，最大化有效遮蔽时长。
- 决策变量：v_U ∈ [70,140]，theta ∈ [0,2π)，t_drop ≥ 0，t_delay ≥ 0（t_det=t_drop+t_delay）
- 适应度：T_obs = sum_{t in [t_det, t_det+20]} IsBlocked(t)
- 优化器：简单PSO骨架（后续可按需增强）

注：不引入第三方库，保持可运行性。
"""

from math import pi
from random import random, uniform
from typing import Tuple
import os, sys

# 允许脚本直接运行：将上级目录 (A题) 加入路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from utils import (
    effective_mask_time,
)

# 固定初始条件
P_M0 = (20000.0, 0.0, 2000.0)
P_U0 = (17800.0, 0.0, 1800.0)

# 搜索边界与常量
g = 9.8
V_MIN, V_MAX = 70.0, 140.0
THETA_MIN, THETA_MAX = 0.0, 2 * pi

# 考虑导弹速度300m/s，导弹到达时间为
# sqrt(2000**2+20000**2)/300 = 66.999s
TDROP_MIN, TDROP_MAX = 0.0, 70.0 

# t = sqrt(2*h/g) = 19.2秒
TDELAY_MIN, TDELAY_MAX = 0.0, 20.0  

# PSO 参数
POP = 100
ITER = 200
w = 0.729
c1 = 1.49
c2 = 1.49


def clamp(x, lo, hi):
    return lo if x < lo else hi if x > hi else x


def fitness(
    v_u: float, theta: float, t_drop: float, t_delay: float, dt: float = 0.01
) -> float:
    t_det = t_drop + t_delay
    # 早期粗过滤：若 t_det 远晚于导弹逼近真目标的时间，可考虑减分（此处先不做复杂逻辑）
    return effective_mask_time(P_M0, P_U0, v_u, theta, t_drop, t_det, dt)


class Particle:
    def __init__(self):
        self.x = [
            uniform(V_MIN, V_MAX),
            uniform(THETA_MIN, THETA_MAX),
            uniform(TDROP_MIN, TDROP_MAX),
            uniform(TDELAY_MIN, TDELAY_MAX),
        ]
        self.v = [0.0, 0.0, 0.0, 0.0]
        self.best_x = self.x[:]
        self.best_f = -1.0

    def eval(self):
        f = fitness(self.x[0], self.x[1], self.x[2], self.x[3])
        if f > self.best_f:
            self.best_f = f
            self.best_x = self.x[:]
        return f


def pso():
    swarm = [Particle() for _ in range(POP)]
    gbest_x = None
    gbest_f = -1.0

    # 初始化评估
    for p in swarm:
        f = p.eval()
        if f > gbest_f:
            gbest_f, gbest_x = f, p.x[:]

    for it in range(ITER):
        for p in swarm:
            for d in range(4):
                r1, r2 = random(), random()
                cognitive = c1 * r1 * (p.best_x[d] - p.x[d])
                social = c2 * r2 * ((gbest_x[d] if gbest_x else p.x[d]) - p.x[d])
                p.v[d] = w * p.v[d] + cognitive + social
                p.x[d] += p.v[d]
            # 变量边界裁剪
            p.x[0] = clamp(p.x[0], V_MIN, V_MAX)
            p.x[1] = p.x[1] % (2 * pi)  # 角度取模
            p.x[2] = clamp(p.x[2], TDROP_MIN, TDROP_MAX)
            p.x[3] = clamp(p.x[3], TDELAY_MIN, TDELAY_MAX)

            f = p.eval()
            if f > gbest_f:
                gbest_f, gbest_x = f, p.x[:]
        print(f"iter={it+1}, best={gbest_f:.4f}")

    return gbest_x, gbest_f


def derive_points(sol: Tuple[float, float, float, float]):
    from utils import P_U, P_G

    v_u, theta, t_drop, t_delay = sol
    t_det = t_drop + t_delay
    P_drop = P_U(P_U0, v_u, theta, t_drop)
    P_det = P_G(P_U0, v_u, theta, t_drop, t_det)
    return P_drop, P_det


if __name__ == "__main__":
    sol, best = pso()
    v_u, theta, t_drop, t_delay = sol
    P_drop, P_det = derive_points(sol)
    print("== 最优策略 ==")
    print(
        f"v_U={v_u:.3f} m/s, theta={theta:.6f} rad, t_drop={t_drop:.3f} s, t_delay={t_delay:.3f} s"
    )
    print(f"P_drop={P_drop}")
    print(f"P_det={P_det}")
    print(f"max_effective_masking_duration={best:.3f} s")
