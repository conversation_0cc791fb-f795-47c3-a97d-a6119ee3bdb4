function solve_problem3()
% SOLVE_PROBLEM3 - 利用遗传算法求解无人机三枚烟幕弹协同干扰问题
%
%   本函数通过调用MATLAB的全局优化工具箱中的ga函数，寻找无人机FY1
%   投放三枚烟幕弹干扰导弹M1的最优策略。
%
%   优化目标: 最大化烟幕对目标的总有效遮蔽时间。
%   决策变量 (8个):
%   x(1): 无人机飞行方向角 phi (弧度, 0 to 2*pi)
%   x(2): 无人机飞行速度 v_UAV (m/s, 70 to 140)
%   x(3): 第一次投弹前飞行时间 t_fly1 (s)
%   x(4): 第1、2次投弹间隔时间 dt_fly2 (s, >= 1)
%   x(5): 第2、3次投弹间隔时间 dt_fly3 (s, >= 1)
%   x(6): 第1枚弹的引爆延迟 t_delay1 (s)
%   x(7): 第2枚弹的引爆延迟 t_delay2 (s)
%   x(8): 第3枚弹的引爆延迟 t_delay3 (s)
%
%   核心技术:
%   1. 向量化: 对时间轴进行向量化计算，避免for循环，大幅提升性能。
%   2. 并行计算: 开启ga的'UseParallel'选项，利用多核CPU加速。

    %% 1. 定义问题常量 (Constants Definition)
    
    % 目标 (被保护的圆柱体)
    P_T_bottom = [0, 200, 0];   % 底部圆心
    P_T_top    = [0, 200, 10];  % 顶部圆心
    P_T_center = [0, 200, 5];   % 几何中心
    R_T = 7;                    % 半径 (m)

    % 威胁 (导弹 M1)
    P_M0 = [20000, 0, 2000];    % 初始位置 (m)
    v_M_scalar = 300;           % 速度大小 (m/s)
    P_Tgt = [0, 0, 0];          % 航向目标点
    
    % 干扰工具 (无人机 FY1 & 烟幕弹)
    P_UAV0 = [17800, 0, 1800];  % 无人机初始位置 (m)
    R_C = 10;                   % 烟幕球半径 (m)
    v_sink = 3;                 % 烟幕下沉速度 (m/s)
    T_smoke_effective = 20;     % 烟幕有效持续时间 (s)
    
    % 物理常量
    g = [0, 0, -9.8];           % 重力加速度 (m/s^2)

    %% 2. 仿真与优化参数设置 (Simulation and GA Settings)
    
    % 仿真时间设置
    dist_M_to_Tgt = norm(P_Tgt - P_M0);
    T_max = dist_M_to_Tgt / v_M_scalar; % 导弹飞行总时间
    dt = 0.1;                           % 时间步长 (s)
    t_vec = (0:dt:T_max)';              % 时间向量 (N x 1)
    
    % 遗传算法参数
    nvars = 8; % 变量数量
    % 变量下界 [phi, v_UAV, t_fly1, dt_fly2, dt_fly3, t_d1, t_d2, t_d3]
    lb = [0,   70,  0,  1,  1,  0,  0,  0]; 
    % 变量上界 (假设飞行和延迟时间不超过总时间的一半)
    ub = [2*pi, 140, T_max/2, T_max/2, T_max/2, T_max/2, T_max/2, T_max/2];

    options = optimoptions('ga', ...
    'Display', 'iter', ...
    'PopulationSize', 2000, ...      % 增加种群
    'MaxGenerations', 300, ...      % 增加代数
    'MutationFcn', @mutationadaptfeasible, ... % 使用自适应变异
    'UseParallel', true, ...
    'HybridFcn', @fmincon);        % <--! 使用混合算法

    %% 3. 执行遗传算法 (Run Genetic Algorithm)
    
    fprintf('开始执行遗传算法优化...\n');
    % ga默认求最小值，因此我们的适应度函数返回负的遮蔽时间
    [best_solution, max_time_neg] = ga(@fitnessFunction, nvars, [], [], [], [], lb, ub, [], options);
    
    max_obscuration_time = -max_time_neg;

    %% 4. 显示与保存结果 (Display and Save Results)
    
    fprintf('\n优化完成。\n');
    fprintf('最大有效遮蔽时间: %.4f 秒\n', max_obscuration_time);
    display_and_save_results(best_solution);
    
    
    %% --- 嵌套函数区域 (Nested Functions Area) ---

    % 适应度函数 (Fitness Function)
    function total_time_neg = fitnessFunction(x)
        % 输入: x - 一个1x8的决策向量 (染色体)
        % 输出: 负的总遮蔽时间
        
        % --- 解码决策变量 ---
        phi       = x(1);
        v_UAV     = x(2);
        t_fly1    = x(3);
        dt_fly2   = x(4);
        dt_fly3   = x(5);
        t_delay1  = x(6);
        t_delay2  = x(7);
        t_delay3  = x(8);

        % --- 向量化计算导弹轨迹 ---
        v_M_unit = (P_Tgt - P_M0) / norm(P_Tgt - P_M0);
        v_M_vec = v_M_scalar * v_M_unit;
        % P_M 是一个 N x 3 矩阵，每行是在一个时间点t_k的[x,y,z]坐标
        P_M = P_M0 + t_vec * v_M_vec;

        % --- 计算三枚烟幕弹的投放与起爆信息 ---
        v_UAV_vec = v_UAV * [cos(phi), sin(phi), 0];
        
        t_drop = [t_fly1; t_fly1 + dt_fly2; t_fly1 + dt_fly2 + dt_fly3];
        t_det = t_drop + [t_delay1; t_delay2; t_delay3];
        
        P_drop = P_UAV0 + t_drop .* v_UAV_vec;
        
        % 抛体运动公式: P_det = P_drop + v_initial*t + 0.5*g*t^2
        delays_vec = [t_delay1; t_delay2; t_delay3];
        P_det = P_drop + delays_vec .* v_UAV_vec + 0.5 * g .* (delays_vec.^2);

        % --- 向量化计算遮蔽状态 ---
        is_obscured_at_t = false(length(t_vec), 1); % 初始化遮蔽状态向量
        
        for i = 1:3 % 遍历三枚烟幕弹
            t_start = t_det(i);
            t_end = t_start + T_smoke_effective;
            
            % 找到当前烟幕弹有效的离散时间点索引
            active_indices = find(t_vec >= t_start & t_vec < t_end);
            
            if isempty(active_indices)
                continue;
            end
            
            % 提取有效时间段内的仿真数据
            t_active = t_vec(active_indices);
            P_M_active = P_M(active_indices, :);
            
            % 计算烟幕球心在这些时间点的轨迹 (匀速下沉)
            time_since_det = t_active - t_det(i);
            P_C_active = P_det(i, :) + time_since_det * [0, 0, -v_sink];
            
            % --- 核心公式: 向量化计算视锥与遮蔽判据 ---
            
            % 1. 计算视锥半角 theta(t)
            % 从导弹指向目标各关键点的向量
            L_center = P_T_center - P_M_active;
            L_top    = P_T_top - P_M_active;
            L_bottom = P_T_bottom - P_M_active;
            
            % 向量化计算距离 (各向量的模)
            dist_M_to_center = vecnorm(L_center, 2, 2);
            dist_M_to_top    = vecnorm(L_top, 2, 2);
            dist_M_to_bottom = vecnorm(L_bottom, 2, 2);

            %% CORE FORMULA: 视锥半角计算
            % alpha是视锥中轴线与"导弹-圆盘中心"连线的夹角
            % theta_subtend是圆盘半径相对于导弹所张的视角
            % 向量点积 A·B = sum(A.*B, 2) 
            alpha_top = acos(sum(L_top .* L_center, 2) ./ (dist_M_to_top .* dist_M_to_center));
            alpha_bottom = acos(sum(L_bottom .* L_center, 2) ./ (dist_M_to_bottom .* dist_M_to_center));
            theta_subtend_top = asin(R_T ./ dist_M_to_top);
            theta_subtend_bottom = asin(R_T ./ dist_M_to_bottom);
            
            % 最终半角是包裹整个目标所需的最大角度
            cone_half_angle = max(alpha_top + theta_subtend_top, alpha_bottom + theta_subtend_bottom);

            % 2. 计算遮蔽判据
            % 烟幕球心到视锥顶点的向量
            L_smoke_to_missile = P_C_active - P_M_active;
            
            % 视锥中轴线单位向量
            cone_axis_unit = L_center ./ dist_M_to_center;

            %% CORE FORMULA: 遮蔽判据计算
            % d_along是球心在轴线上的投影长度
            d_along = sum(L_smoke_to_missile .* cone_axis_unit, 2);
            
            % d_axis是球心到轴线的垂直距离 (勾股定理)
            d_axis_sq = vecnorm(L_smoke_to_missile, 2, 2).^2 - d_along.^2;
            d_axis = sqrt(max(0, d_axis_sq)); % max(0,...) 避免浮点误差导致负数
            
            % R_cone是投影点处的视锥半径
            R_cone = d_along .* tan(cone_half_angle);
            
            % 遮蔽条件: 球心到轴线距离 <= 烟幕球半径 - 视锥在该处半径
            is_bomb_i_obscuring = (d_axis <= R_C - R_cone) & (d_along > 0);
            
            % --- 更新总遮蔽状态向量 ---
            % 使用逻辑或运算，实现时间区间的并集
            is_obscured_at_t(active_indices) = is_obscured_at_t(active_indices) | is_bomb_i_obscuring;
        end
        
        % 计算总时间 (被遮蔽的时间步数 * 每步时长)
        total_time = sum(is_obscured_at_t) * dt;
        
        % ga求最小值，故返回负值
        total_time_neg = -total_time;
    end
    
    % 结果展示与保存函数
    function display_and_save_results(s)
        % s: a 1x8 solution vector
        fprintf('--- 最优投放策略 ---\n');
        fprintf('无人机飞行参数:\n');
        fprintf('  - 飞行方向 (度): %.4f\n', rad2deg(s(1)));
        fprintf('  - 飞行速度 (m/s): %.4f\n', s(2));
        
        v_uav_res = s(2) * [cos(s(1)), sin(s(1)), 0];
        
        t_drop1 = s(3);
        t_drop2 = s(3) + s(4);
        t_drop3 = s(3) + s(4) + s(5);
        
        P_drop1 = P_UAV0 + t_drop1 * v_uav_res;
        P_drop2 = P_UAV0 + t_drop2 * v_uav_res;
        P_drop3 = P_UAV0 + t_drop3 * v_uav_res;
        
        t_det1 = t_drop1 + s(6); P_det1 = P_drop1 + s(6)*v_uav_res + 0.5*g*s(6)^2;
        t_det2 = t_drop2 + s(7); P_det2 = P_drop2 + s(7)*v_uav_res + 0.5*g*s(7)^2;
        t_det3 = t_drop3 + s(8); P_det3 = P_drop3 + s(8)*v_uav_res + 0.5*g*s(8)^2;

        fprintf('\n烟幕弹 1:\n');
        fprintf('  - 投弹时刻: %.4f s\n', t_drop1);
        fprintf('  - 投弹位置 (x,y,z): (%.2f, %.2f, %.2f)\n', P_drop1);
        fprintf('  - 引爆延迟: %.4f s\n', s(6));
        fprintf('  - 起爆时刻: %.4f s\n', t_det1);
        fprintf('  - 起爆位置 (x,y,z): (%.2f, %.2f, %.2f)\n', P_det1);

        fprintf('\n烟幕弹 2:\n');
        fprintf('  - 投弹时刻: %.4f s\n', t_drop2);
        fprintf('  - 投弹位置 (x,y,z): (%.2f, %.2f, %.2f)\n', P_drop2);
        fprintf('  - 引爆延迟: %.4f s\n', s(7));
        fprintf('  - 起爆时刻: %.4f s\n', t_det2);
        fprintf('  - 起爆位置 (x,y,z): (%.2f, %.2f, %.2f)\n', P_det2);
        
        fprintf('\n烟幕弹 3:\n');
        fprintf('  - 投弹时刻: %.4f s\n', t_drop3);
        fprintf('  - 投弹位置 (x,y,z): (%.2f, %.2f, %.2f)\n', P_drop3);
        fprintf('  - 引爆延迟: %.4f s\n', s(8));
        fprintf('  - 起爆时刻: %.4f s\n', t_det3);
        fprintf('  - 起爆位置 (x,y,z): (%.2f, %.2f, %.2f)\n', P_det3);

        % 保存到 Excel
        try
            filename = 'result1.xlsx';
            headers = {'参数', '数值', '单位'};
            data = {
                '无人机飞行方向', rad2deg(s(1)), '度';
                '无人机飞行速度', s(2), 'm/s';
                '-- 烟幕弹1 --', '', '';
                '投弹时刻', t_drop1, 's';
                '投弹位置X', P_drop1(1), 'm';
                '投弹位置Y', P_drop1(2), 'm';
                '投弹位置Z', P_drop1(3), 'm';
                '引爆延迟', s(6), 's';
                '-- 烟幕弹2 --', '', '';
                '投弹时刻', t_drop2, 's';
                '投弹位置X', P_drop2(1), 'm';
                '投弹位置Y', P_drop2(2), 'm';
                '投弹位置Z', P_drop2(3), 'm';
                '引爆延迟', s(7), 's';
                '-- 烟幕弹3 --', '', '';
                '投弹时刻', t_drop3, 's';
                '投弹位置X', P_drop3(1), 'm';
                '投弹位置Y', P_drop3(2), 'm';
                '投弹位置Z', P_drop3(3), 'm';
                '引爆延迟', s(8), 's';
                '--', '', '';
                '总有效遮蔽时间', max_obscuration_time, 's'
            };
            
            T = cell2table(data, 'VariableNames', headers);
            writetable(T, filename, 'Sheet', 1);
            fprintf('\n结果已成功保存到 %s\n', filename);
        catch ME
            fprintf('\n无法保存到Excel文件: %s\n', ME.message);
        end
    end

end