"""
分别导出四张图片的中文和英文版本（无标题）
总共导出8张图片
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.patches as patches
from matplotlib.patches import Circle
import warnings
warnings.filterwarnings('ignore')

# 导入问题1的核心函数
from problem1 import *

# 设置基本样式
import matplotlib
matplotlib.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 12
plt.rcParams['axes.linewidth'] = 1.0
plt.rcParams['lines.linewidth'] = 2.0

def set_chinese_font():
    """设置中文字体"""
    matplotlib.rcParams['font.family'] = ['SimHei', 'DejaVu Sans']

def set_english_font():
    """设置英文字体"""
    matplotlib.rcParams['font.family'] = ['Times New Roman', 'DejaVu Sans']

def plot_geometric_model(ax, language='en'):
    """几何模型图"""
    ax.set_xlim(-1, 7)
    ax.set_ylim(-2.5, 2.5)
    
    # 视锥参数
    vertex = np.array([0, 0])
    cone_angle = 0.5
    cone_length = 5.5
    
    # 绘制视锥
    upper_bound = vertex + cone_length * np.array([np.cos(cone_angle), np.sin(cone_angle)])
    lower_bound = vertex + cone_length * np.array([np.cos(-cone_angle), np.sin(-cone_angle)])
    
    if language == 'zh':
        ax.plot([vertex[0], upper_bound[0]], [vertex[1], upper_bound[1]], 'r-', linewidth=2.5, label='视锥边界')
        ax.plot([vertex[0], lower_bound[0]], [vertex[1], lower_bound[1]], 'r-', linewidth=2.5)
        ax.plot([vertex[0], cone_length], [vertex[1], 0], 'r--', linewidth=1.5, alpha=0.7, label='视锥轴线')
    else:
        ax.plot([vertex[0], upper_bound[0]], [vertex[1], upper_bound[1]], 'r-', linewidth=2.5, label='Sight Cone Boundary')
        ax.plot([vertex[0], lower_bound[0]], [vertex[1], lower_bound[1]], 'r-', linewidth=2.5)
        ax.plot([vertex[0], cone_length], [vertex[1], 0], 'r--', linewidth=1.5, alpha=0.7, label='Cone Axis')
    
    # 烟幕球
    smoke_center = np.array([3.5, 0.3])
    smoke_radius = 0.8
    if language == 'zh':
        circle = Circle(smoke_center, smoke_radius, fill=False, color='orange', linewidth=3, label='烟幕球')
    else:
        circle = Circle(smoke_center, smoke_radius, fill=False, color='orange', linewidth=3, label='Smoke Sphere')
    ax.add_patch(circle)
    
    # 目标点
    target = np.array([5.5, 0])
    if language == 'zh':
        ax.scatter(*target, color='purple', s=180, marker='*', label='保护目标', zorder=5)
    else:
        ax.scatter(*target, color='purple', s=180, marker='*', label='Protected Target', zorder=5)
    
    # 关键几何量标注
    if language == 'zh':
        ax.annotate('导弹', vertex, xytext=(-0.3, -0.8), fontsize=11, ha='center',
                    arrowprops=dict(arrowstyle='->', color='red', lw=1.5))
    else:
        ax.annotate('Missile', vertex, xytext=(-0.3, -0.8), fontsize=11, ha='center',
                    arrowprops=dict(arrowstyle='->', color='red', lw=1.5))
    
    ax.annotate('$R_C$', smoke_center + np.array([0.6, 0.6]), fontsize=12, ha='center', color='orange', weight='bold')
    
    # 距离标注
    ax.plot([smoke_center[0], smoke_center[0]], [0, smoke_center[1]], 'b--', alpha=0.7, linewidth=1.5)
    ax.annotate('$d_{axis}$', [smoke_center[0], -1.8], fontsize=12, ha='center', color='blue', weight='bold')
    
    # 视锥半径标注
    cone_radius_point = np.array([smoke_center[0], cone_length * np.tan(cone_angle) * smoke_center[0] / cone_length])
    ax.plot([smoke_center[0], cone_radius_point[0]], [0, cone_radius_point[1]], 'g--', alpha=0.7, linewidth=1.5)
    ax.annotate('$R_{cone}$', [smoke_center[0] + 0.3, cone_radius_point[1] + 0.2], fontsize=12, ha='center', color='green', weight='bold')
    
    if language == 'zh':
        ax.set_xlabel('距离 (相对单位)', fontsize=12)
        ax.set_ylabel('距离 (相对单位)', fontsize=12)
    else:
        ax.set_xlabel('Distance (relative units)', fontsize=12)
        ax.set_ylabel('Distance (relative units)', fontsize=12)
    
    ax.legend(loc='upper left', fontsize=10)
    ax.grid(True, alpha=0.3)
    ax.set_aspect('equal')

def plot_3d_trajectories(ax, language='en'):
    """3D轨迹图"""
    # 轨迹计算
    t_range = np.linspace(0, 20, 80)
    missile_traj = np.array([P_M(t) for t in t_range])
    
    # 烟幕轨迹
    t_smoke = t_range[t_range >= t_det]
    if len(t_smoke) > 0:
        smoke_traj = np.array([P_C(t) for t in t_smoke])
        if language == 'zh':
            ax.plot(smoke_traj[:, 0], smoke_traj[:, 1], smoke_traj[:, 2], 
                    'orange', linewidth=3.5, label='烟幕轨迹', alpha=0.9)
        else:
            ax.plot(smoke_traj[:, 0], smoke_traj[:, 1], smoke_traj[:, 2], 
                    'orange', linewidth=3.5, label='Smoke Trajectory', alpha=0.9)
    
    # 导弹轨迹
    if language == 'zh':
        ax.plot(missile_traj[:, 0], missile_traj[:, 1], missile_traj[:, 2], 
                'r-', linewidth=2.5, label='导弹轨迹', alpha=0.8)
    else:
        ax.plot(missile_traj[:, 0], missile_traj[:, 1], missile_traj[:, 2], 
                'r-', linewidth=2.5, label='Missile Trajectory', alpha=0.8)
    
    # 关键点标记
    if language == 'zh':
        ax.scatter(*P_M0, color='red', s=100, marker='^', label='M1起点')
        ax.scatter(*P_U0, color='blue', s=100, marker='s', label='FY1起点')
        ax.scatter(*P_T_center, color='purple', s=150, marker='*', label='目标')
    else:
        ax.scatter(*P_M0, color='red', s=100, marker='^', label='M1 Start')
        ax.scatter(*P_U0, color='blue', s=100, marker='s', label='FY1 Start')
        ax.scatter(*P_T_center, color='purple', s=150, marker='*', label='Target')
    
    # 目标圆柱体轮廓
    theta = np.linspace(0, 2*np.pi, 16)
    x_cyl = P_T_bottom[0] + R_T * np.cos(theta)
    y_cyl = P_T_bottom[1] + R_T * np.sin(theta)
    z_bottom = np.full_like(x_cyl, P_T_bottom[2])
    z_top = np.full_like(x_cyl, P_T_bottom[2] + H_T)
    
    ax.plot(x_cyl, y_cyl, z_bottom, 'purple', alpha=0.6, linewidth=1.5)
    ax.plot(x_cyl, y_cyl, z_top, 'purple', alpha=0.6, linewidth=1.5)
    
    ax.set_xlabel('X (m)', fontsize=12)
    ax.set_ylabel('Y (m)', fontsize=12)
    ax.set_zlabel('Z (m)', fontsize=12)
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)
    ax.view_init(elev=15, azim=45)

def plot_blocking_timeline(ax, language='en'):
    """遮蔽效果时间线"""
    t_range = np.linspace(t_det, t_det + T_eff, 800)
    blocking_status = [is_blocked(t) for t in t_range]
    
    # 绘制遮蔽状态
    if language == 'zh':
        ax.fill_between(t_range, 0, blocking_status, alpha=0.6, color='lightgreen', 
                        label='有效遮蔽时段')
    else:
        ax.fill_between(t_range, 0, blocking_status, alpha=0.6, color='lightgreen', 
                        label='Effective Blocking Period')
    ax.plot(t_range, blocking_status, 'darkgreen', linewidth=2.5)
    
    # 计算关键数值
    dt = t_range[1] - t_range[0]
    total_time = sum(blocking_status) * dt
    efficiency = total_time / T_eff * 100
    
    # 添加数值标注
    if language == 'zh':
        textstr = f'总遮蔽时间: {total_time:.2f}秒\n遮蔽效率: {efficiency:.1f}%'
    else:
        textstr = f'Total Blocking Time: {total_time:.2f}s\nBlocking Efficiency: {efficiency:.1f}%'
    
    props = dict(boxstyle='round', facecolor='wheat', alpha=0.8)
    ax.text(0.05, 0.95, textstr, transform=ax.transAxes, fontsize=11,
            verticalalignment='top', bbox=props)
    
    # 时间标记
    if language == 'zh':
        ax.axvline(x=t_det, color='red', linestyle='--', alpha=0.7, label='起爆时刻')
        ax.axvline(x=t_det + T_eff, color='red', linestyle='--', alpha=0.7, label='失效时刻')
    else:
        ax.axvline(x=t_det, color='red', linestyle='--', alpha=0.7, label='Detonation')
        ax.axvline(x=t_det + T_eff, color='red', linestyle='--', alpha=0.7, label='Expiration')
    
    if language == 'zh':
        ax.set_xlabel('时间 (秒)', fontsize=12)
        ax.set_ylabel('遮蔽状态', fontsize=12)
    else:
        ax.set_xlabel('Time (s)', fontsize=12)
        ax.set_ylabel('Blocking Status', fontsize=12)
    
    ax.set_ylim(-0.05, 1.05)
    ax.legend(fontsize=10)
    ax.grid(True, alpha=0.3)

def plot_parameter_evolution(ax, language='en'):
    """参数演化图"""
    t_range = np.linspace(t_det, t_det + T_eff, 150)
    
    d_axis_vals = []
    r_cone_vals = []
    sum_vals = []
    
    for t in t_range:
        V = P_M(t)
        A = view_cone_axis(t)
        theta = view_cone_half_angle(t)
        C = P_C(t)
        
        L = sub(C, V)
        d_along = dot(L, A)
        
        if d_along > 0:
            L2 = dot(L, L)
            perp2 = max(L2 - d_along * d_along, 0.0)
            d_axis = sqrt(perp2)
            R_cone = d_along * tan(theta)
            sum_val = d_axis + R_cone
        else:
            d_axis = np.nan
            R_cone = 0
            sum_val = np.nan
            
        d_axis_vals.append(d_axis)
        r_cone_vals.append(R_cone)
        sum_vals.append(sum_val)
    
    # 绘制参数曲线
    ax.plot(t_range, d_axis_vals, 'b-', linewidth=2.5, label='$d_{axis}(t)$')
    ax.plot(t_range, r_cone_vals, 'r-', linewidth=2.5, label='$R_{cone}(t)$')
    ax.plot(t_range, sum_vals, 'purple', linewidth=2.5, label='$d_{axis} + R_{cone}$')
    
    if language == 'zh':
        ax.axhline(y=R_C, color='orange', linestyle='--', linewidth=2.5, label='$R_C$ (烟幕半径)')
    else:
        ax.axhline(y=R_C, color='orange', linestyle='--', linewidth=2.5, label='$R_C$ (Smoke Radius)')
    
    # 遮蔽条件区域
    valid_mask = ~np.isnan(sum_vals)
    blocked_mask = np.array(sum_vals) <= R_C
    combined_mask = valid_mask & blocked_mask
    
    if np.any(combined_mask):
        if language == 'zh':
            ax.fill_between(t_range, 0, 12, where=combined_mask, alpha=0.2, color='green', 
                            label='遮蔽条件满足')
        else:
            ax.fill_between(t_range, 0, 12, where=combined_mask, alpha=0.2, color='green', 
                            label='Blocking Condition Met')
    
    if language == 'zh':
        ax.set_xlabel('时间 (秒)', fontsize=12)
        ax.set_ylabel('距离 (米)', fontsize=12)
    else:
        ax.set_xlabel('Time (s)', fontsize=12)
        ax.set_ylabel('Distance (m)', fontsize=12)
    
    ax.legend(fontsize=10)
    ax.grid(True, alpha=0.3)
    ax.set_ylim(0, 12)

def export_individual_figures():
    """导出单独的图片"""
    
    # 图片尺寸设置
    fig_size = (8, 6)
    
    # 1. 几何模型图 - 中文版
    set_chinese_font()
    fig1_zh = plt.figure(figsize=fig_size)
    ax1_zh = fig1_zh.add_subplot(111)
    plot_geometric_model(ax1_zh, 'zh')
    plt.tight_layout()
    fig1_zh.savefig('figure1_geometric_model_zh.png', dpi=300, bbox_inches='tight', facecolor='white')
    fig1_zh.savefig('figure1_geometric_model_zh.pdf', bbox_inches='tight', facecolor='white')
    plt.close(fig1_zh)
    
    # 1. 几何模型图 - 英文版
    set_english_font()
    fig1_en = plt.figure(figsize=fig_size)
    ax1_en = fig1_en.add_subplot(111)
    plot_geometric_model(ax1_en, 'en')
    plt.tight_layout()
    fig1_en.savefig('figure1_geometric_model_en.png', dpi=300, bbox_inches='tight', facecolor='white')
    fig1_en.savefig('figure1_geometric_model_en.pdf', bbox_inches='tight', facecolor='white')
    plt.close(fig1_en)
    
    # 2. 3D轨迹图 - 中文版
    set_chinese_font()
    fig2_zh = plt.figure(figsize=fig_size)
    ax2_zh = fig2_zh.add_subplot(111, projection='3d')
    plot_3d_trajectories(ax2_zh, 'zh')
    plt.tight_layout()
    fig2_zh.savefig('figure2_3d_trajectories_zh.png', dpi=300, bbox_inches='tight', facecolor='white')
    fig2_zh.savefig('figure2_3d_trajectories_zh.pdf', bbox_inches='tight', facecolor='white')
    plt.close(fig2_zh)
    
    # 2. 3D轨迹图 - 英文版
    set_english_font()
    fig2_en = plt.figure(figsize=fig_size)
    ax2_en = fig2_en.add_subplot(111, projection='3d')
    plot_3d_trajectories(ax2_en, 'en')
    plt.tight_layout()
    fig2_en.savefig('figure2_3d_trajectories_en.png', dpi=300, bbox_inches='tight', facecolor='white')
    fig2_en.savefig('figure2_3d_trajectories_en.pdf', bbox_inches='tight', facecolor='white')
    plt.close(fig2_en)
    
    # 3. 遮蔽效果图 - 中文版
    set_chinese_font()
    fig3_zh = plt.figure(figsize=fig_size)
    ax3_zh = fig3_zh.add_subplot(111)
    plot_blocking_timeline(ax3_zh, 'zh')
    plt.tight_layout()
    fig3_zh.savefig('figure3_blocking_timeline_zh.png', dpi=300, bbox_inches='tight', facecolor='white')
    fig3_zh.savefig('figure3_blocking_timeline_zh.pdf', bbox_inches='tight', facecolor='white')
    plt.close(fig3_zh)
    
    # 3. 遮蔽效果图 - 英文版
    set_english_font()
    fig3_en = plt.figure(figsize=fig_size)
    ax3_en = fig3_en.add_subplot(111)
    plot_blocking_timeline(ax3_en, 'en')
    plt.tight_layout()
    fig3_en.savefig('figure3_blocking_timeline_en.png', dpi=300, bbox_inches='tight', facecolor='white')
    fig3_en.savefig('figure3_blocking_timeline_en.pdf', bbox_inches='tight', facecolor='white')
    plt.close(fig3_en)
    
    # 4. 参数演化图 - 中文版
    set_chinese_font()
    fig4_zh = plt.figure(figsize=fig_size)
    ax4_zh = fig4_zh.add_subplot(111)
    plot_parameter_evolution(ax4_zh, 'zh')
    plt.tight_layout()
    fig4_zh.savefig('figure4_parameter_evolution_zh.png', dpi=300, bbox_inches='tight', facecolor='white')
    fig4_zh.savefig('figure4_parameter_evolution_zh.pdf', bbox_inches='tight', facecolor='white')
    plt.close(fig4_zh)
    
    # 4. 参数演化图 - 英文版
    set_english_font()
    fig4_en = plt.figure(figsize=fig_size)
    ax4_en = fig4_en.add_subplot(111)
    plot_parameter_evolution(ax4_en, 'en')
    plt.tight_layout()
    fig4_en.savefig('figure4_parameter_evolution_en.png', dpi=300, bbox_inches='tight', facecolor='white')
    fig4_en.savefig('figure4_parameter_evolution_en.pdf', bbox_inches='tight', facecolor='white')
    plt.close(fig4_en)

if __name__ == "__main__":
    print("正在导出单独图片...")
    export_individual_figures()
    
    print("\n成功导出8张图片:")
    print("中文版本:")
    print("1. figure1_geometric_model_zh.png/pdf - 几何模型图")
    print("2. figure2_3d_trajectories_zh.png/pdf - 3D轨迹图")
    print("3. figure3_blocking_timeline_zh.png/pdf - 遮蔽效果图")
    print("4. figure4_parameter_evolution_zh.png/pdf - 参数演化图")
    print("\n英文版本:")
    print("1. figure1_geometric_model_en.png/pdf - Geometric Model")
    print("2. figure2_3d_trajectories_en.png/pdf - 3D Trajectories")
    print("3. figure3_blocking_timeline_en.png/pdf - Blocking Timeline")
    print("4. figure4_parameter_evolution_en.png/pdf - Parameter Evolution")
    print("\n所有图片均为高分辨率(300 DPI)，适合论文使用！")
