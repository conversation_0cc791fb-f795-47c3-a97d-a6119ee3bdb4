import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from mpl_toolkits.mplot3d.art3d import Line3DCollection

# ---------- 参数 ----------
R_T, H_T = 7, 10                      # 目标尺寸
C_bottom = np.array([0, 200, 0])
C_top    = np.array([0, 200, 10])
C_center = (C_bottom + C_top)/2

v_M = 300
v_U = 120
t_drop = 1.5
t_det  = 5.1
R_C = 10
v_sink = 3

# 导弹初始 & 方向
P_M0   = np.array([20000, 0, 2000])
dir_M  = -P_M0/np.linalg.norm(P_M0)  # 指向原点

# UAV 初始 & 方向
P_U0  = np.array([17800, 0, 1800])
dir_U = np.array([-1, 0, 0])

# ---------- 运动学函数 ----------
P_M = lambda t: P_M0 + v_M*t*dir_M
P_U = lambda t: P_U0 + v_U*t*dir_U
P_G = lambda t: P_U(t_drop) + dir_U*v_U*(t-t_drop) + np.array([0,0,-0.5*9.8*(t-t_drop)**2]) if t>=t_drop else P_U(t)
P_C = lambda t: P_G(t_det) + np.array([0,0,-v_sink*(t-t_det)]) if t>=t_det else P_G(t_det)

# ---------- 视锥参数 ----------
def cone_data(t):
    V = P_M(t)
    A = (C_center - V)/np.linalg.norm(C_center - V)
    d_top = np.linalg.norm(C_top - V)
    d_bot = np.linalg.norm(C_bottom - V)
    th_top = np.arcsin(R_T/d_top)
    th_bot = np.arcsin(R_T/d_bot)
    alpha_top = np.arccos(np.clip(np.dot(A, (C_top - V)/d_top), -1, 1))
    alpha_bot = np.arccos(np.clip(np.dot(A, (C_bottom - V)/d_bot), -1, 1))
    theta = max(alpha_top + th_top, alpha_bot + th_bot)
    return V, A, theta

# ---------- 画图 ----------
fig = plt.figure(figsize=(14, 10))
ax = fig.add_subplot(111, projection='3d')

# 1. 圆柱体
z_cyl = np.linspace(0, H_T, 30)
th_cyl = np.linspace(0, 2*np.pi, 40)
T, Z = np.meshgrid(th_cyl, z_cyl)
X_cyl = R_T * np.cos(T)
Y_cyl = 200 + R_T * np.sin(T)
Z_cyl = Z
ax.plot_surface(X_cyl, Y_cyl, Z_cyl, color='grey', alpha=0.25, lw=0.1)

# 2. 导弹轨迹（直线段）
t_max = 25
t_traj = np.linspace(0, t_max, 200)
missile_path = np.array([P_M(t) for t in t_traj])
ax.plot(*missile_path.T, color='red', lw=1.5, label='Missile trajectory')

# 3. 当前时刻 t=7 s
t_now = 7.0
V_now, A_now, theta_now = cone_data(t_now)
C_now = P_C(t_now)

# 导弹当前点
ax.scatter(*V_now, color='red', s=80, label='Missile now')
ax.scatter(*C_center, color='blue', s=80, label='Target center')
ax.scatter(*C_now,  color='green', s=80, label='Sphere center')

# 4. 视锥边界（两条切线）
L_len = 800   # 只想画一段示意
cone_line1 = lambda l: V_now + l*(np.cos(theta_now)*A_now + np.sin(theta_now)*np.cross(A_now,[0,0,1]))
cone_line2 = lambda l: V_now + l*(np.cos(theta_now)*A_now - np.sin(theta_now)*np.cross(A_now,[0,0,1]))
l_vals = np.linspace(0, L_len, 100)
line1 = np.array([cone_line1(l) for l in l_vals])
line2 = np.array([cone_line2(l) for l in l_vals])
ax.plot(*line1.T, color='black', lw=1, alpha=0.7)
ax.plot(*line2.T, color='black', lw=1, alpha=0.7)
ax.quiver(*V_now, *(A_now*400), color='k', arrow_length_ratio=0.03, lw=2, label='Sight axis')

# 5. 投放-起爆-当前 三点连线
P_drop = P_U(t_drop)
P_det  = P_G(t_det)
ax.scatter(*P_drop, color='orange', s=60)
ax.scatter(*P_det,  color='purple', s=60)
ax.plot([P_drop[0], P_det[0]], [P_drop[1], P_det[1]], [P_drop[2], P_det[2]], 'o--', color='purple', lw=2, alpha=0.8, label='UAV → bomb')

# 6. 烟幕球（真实半径）
u, v = np.mgrid[0:2*np.pi:30j, 0:np.pi:15j]
sphere_x = C_now[0] + R_C*np.cos(u)*np.sin(v)
sphere_y = C_now[1] + R_C*np.sin(u)*np.sin(v)
sphere_z = C_now[2] + R_C*np.cos(v)
ax.plot_surface(sphere_x, sphere_y, sphere_z, color='green', alpha=0.25, lw=0.1)

# ---------- 局部放大 ----------
# 计算包围盒
zoom_center = (V_now + C_now)/2
zoom_size = 200
ax.set_xlim(zoom_center[0]-zoom_size, zoom_center[0]+zoom_size)
ax.set_ylim(zoom_center[1]-zoom_size, zoom_center[1]+zoom_size)
ax.set_zlim(zoom_center[2]-zoom_size, zoom_center[2]+zoom_size)

# 若还想额外插入子图，可再用 inset-axis（此处略）

# ---------- 美化 ----------
ax.set_xlabel('X / m')
ax.set_ylabel('Y / m')
ax.set_zlabel('Z / m')
ax.set_title('Problem 1 – Refined 3D Geometry & Sight Cone')
ax.legend(loc='upper left', fontsize=9)
plt.tight_layout()
plt.savefig('problem1_refined.png', dpi=450, bbox_inches='tight')
plt.show()