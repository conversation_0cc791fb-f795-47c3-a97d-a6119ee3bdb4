"""
导出美化版单独图片 - 中英文版本
重新理解有效遮蔽概念，确保数据准确性
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.patches as patches
from matplotlib.patches import Circle
import warnings
warnings.filterwarnings('ignore')

# 导入问题1的核心函数
from problem1 import *

# 设置现代化图表样式
import matplotlib
matplotlib.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 13
plt.rcParams['axes.linewidth'] = 1.2
plt.rcParams['lines.linewidth'] = 2.5
plt.rcParams['axes.grid'] = True
plt.rcParams['grid.alpha'] = 0.3
plt.rcParams['axes.axisbelow'] = True

# 美观配色方案
COLORS = {
    'missile': '#E74C3C',      # 鲜红色 - 导弹
    'smoke': '#F39C12',        # 橙色 - 烟幕
    'target': '#8E44AD',       # 紫色 - 目标
    'uav': '#3498DB',          # 蓝色 - 无人机
    'cone': '#E74C3C',         # 红色 - 视锥
    'axis': '#C0392B',         # 深红色 - 轴线
    'blocking': '#27AE60',     # 绿色 - 遮蔽
}

def set_chinese_font():
    """设置中文字体"""
    matplotlib.rcParams['font.family'] = ['SimHei', 'DejaVu Sans']

def set_english_font():
    """设置英文字体"""
    matplotlib.rcParams['font.family'] = ['Times New Roman', 'DejaVu Sans']

def plot_enhanced_geometric_model(ax, language='en'):
    """增强版几何模型图"""
    ax.set_xlim(-1.5, 8)
    ax.set_ylim(-3, 3)
    ax.set_facecolor('#FAFAFA')
    
    # 视锥参数
    vertex = np.array([0, 0])
    cone_angle = 0.4  # 约23度
    cone_length = 6.5
    
    # 绘制视锥填充区域
    upper_bound = vertex + cone_length * np.array([np.cos(cone_angle), np.sin(cone_angle)])
    lower_bound = vertex + cone_length * np.array([np.cos(-cone_angle), np.sin(-cone_angle)])
    
    # 视锥填充
    cone_x = [vertex[0], upper_bound[0], lower_bound[0], vertex[0]]
    cone_y = [vertex[1], upper_bound[1], lower_bound[1], vertex[1]]
    
    if language == 'zh':
        ax.fill(cone_x, cone_y, color=COLORS['cone'], alpha=0.15, label='视锥区域')
        ax.plot([vertex[0], upper_bound[0]], [vertex[1], upper_bound[1]], 
                color=COLORS['cone'], linewidth=3, label='视锥边界')
        ax.plot([vertex[0], lower_bound[0]], [vertex[1], lower_bound[1]], 
                color=COLORS['cone'], linewidth=3)
        ax.plot([vertex[0], cone_length], [vertex[1], 0], 
                color=COLORS['axis'], linewidth=2, linestyle='--', alpha=0.8, label='视锥轴线')
    else:
        ax.fill(cone_x, cone_y, color=COLORS['cone'], alpha=0.15, label='Sight Cone')
        ax.plot([vertex[0], upper_bound[0]], [vertex[1], upper_bound[1]], 
                color=COLORS['cone'], linewidth=3, label='Cone Boundary')
        ax.plot([vertex[0], lower_bound[0]], [vertex[1], lower_bound[1]], 
                color=COLORS['cone'], linewidth=3)
        ax.plot([vertex[0], cone_length], [vertex[1], 0], 
                color=COLORS['axis'], linewidth=2, linestyle='--', alpha=0.8, label='Cone Axis')
    
    # 烟幕球 - 有效遮蔽示例
    smoke_center = np.array([3.2, 0.2])
    smoke_radius = 1.0
    
    # 计算几何参数
    cone_radius = smoke_center[0] * np.tan(cone_angle)
    d_axis = abs(smoke_center[1])
    
    # 绘制烟幕球
    if language == 'zh':
        circle = Circle(smoke_center, smoke_radius, fill=True, 
                       facecolor=COLORS['smoke'], alpha=0.3, 
                       edgecolor=COLORS['smoke'], linewidth=3, label='烟幕球')
    else:
        circle = Circle(smoke_center, smoke_radius, fill=True, 
                       facecolor=COLORS['smoke'], alpha=0.3, 
                       edgecolor=COLORS['smoke'], linewidth=3, label='Smoke Sphere')
    ax.add_patch(circle)
    
    # 目标点
    target = np.array([6.5, 0])
    if language == 'zh':
        ax.scatter(*target, color=COLORS['target'], s=200, marker='*', 
                  label='保护目标', zorder=5, edgecolors='white', linewidth=2)
    else:
        ax.scatter(*target, color=COLORS['target'], s=200, marker='*', 
                  label='Protected Target', zorder=5, edgecolors='white', linewidth=2)
    
    # 导弹标注
    if language == 'zh':
        ax.annotate('导弹', vertex, xytext=(-0.5, -1.0), fontsize=12, ha='center',
                    arrowprops=dict(arrowstyle='->', color=COLORS['missile'], lw=2),
                    bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
    else:
        ax.annotate('Missile', vertex, xytext=(-0.5, -1.0), fontsize=12, ha='center',
                    arrowprops=dict(arrowstyle='->', color=COLORS['missile'], lw=2),
                    bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
    
    # 几何量标注
    ax.plot([smoke_center[0], smoke_center[0]], [0, smoke_center[1]], 
            'b-', alpha=0.8, linewidth=2)
    ax.annotate(f'$d_{{axis}}$ = {d_axis:.1f}', 
                [smoke_center[0] - 0.3, smoke_center[1]/2], 
                fontsize=12, ha='center', color='blue', weight='bold',
                bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8))
    
    ax.plot([smoke_center[0], smoke_center[0]], [0, cone_radius], 
            'g-', alpha=0.8, linewidth=2)
    ax.annotate(f'$R_{{cone}}$ = {cone_radius:.1f}', 
                [smoke_center[0] + 0.4, cone_radius/2], 
                fontsize=12, ha='center', color='green', weight='bold',
                bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8))
    
    ax.annotate(f'$R_C$ = {smoke_radius:.1f}', 
                smoke_center + np.array([0.7, 0.7]), 
                fontsize=12, ha='center', color=COLORS['smoke'], weight='bold',
                bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8))
    
    # 遮蔽条件标注
    condition = d_axis + cone_radius <= smoke_radius
    condition_text = f'{d_axis:.1f} + {cone_radius:.1f} = {d_axis + cone_radius:.1f} ≤ {smoke_radius:.1f}'
    
    if language == 'zh':
        ax.text(3.2, -2.3, f'遮蔽条件: {condition_text}', 
                fontsize=11, ha='center', 
                color='green' if condition else 'red', weight='bold',
                bbox=dict(boxstyle="round,pad=0.3", 
                         facecolor='lightgreen' if condition else 'lightcoral', alpha=0.8))
        ax.set_xlabel('距离 (相对单位)', fontsize=13, weight='bold')
        ax.set_ylabel('距离 (相对单位)', fontsize=13, weight='bold')
    else:
        ax.text(3.2, -2.3, f'Blocking Condition: {condition_text}', 
                fontsize=11, ha='center', 
                color='green' if condition else 'red', weight='bold',
                bbox=dict(boxstyle="round,pad=0.3", 
                         facecolor='lightgreen' if condition else 'lightcoral', alpha=0.8))
        ax.set_xlabel('Distance (relative units)', fontsize=13, weight='bold')
        ax.set_ylabel('Distance (relative units)', fontsize=13, weight='bold')
    
    ax.legend(loc='upper left', fontsize=11, framealpha=0.9)
    ax.grid(True, alpha=0.4, linestyle='-', linewidth=0.5)
    ax.set_aspect('equal')

def plot_enhanced_3d_trajectories(ax, language='en'):
    """美化版3D轨迹图"""
    ax.set_facecolor('#F8F9FA')
    
    # 真实轨迹数据
    t_range = np.linspace(0, 30, 150)
    missile_traj = np.array([P_M(t) for t in t_range])
    
    # 烟幕轨迹
    t_smoke = t_range[(t_range >= t_det) & (t_range <= t_det + T_eff)]
    if len(t_smoke) > 0:
        smoke_traj = np.array([P_C(t) for t in t_smoke])
        if language == 'zh':
            ax.plot(smoke_traj[:, 0], smoke_traj[:, 1], smoke_traj[:, 2], 
                    color=COLORS['smoke'], linewidth=4, alpha=0.8, label='烟幕轨迹')
        else:
            ax.plot(smoke_traj[:, 0], smoke_traj[:, 1], smoke_traj[:, 2], 
                    color=COLORS['smoke'], linewidth=4, alpha=0.8, label='Smoke Trajectory')
    
    # 导弹轨迹
    if language == 'zh':
        ax.plot(missile_traj[:, 0], missile_traj[:, 1], missile_traj[:, 2], 
                color=COLORS['missile'], linewidth=3, alpha=0.8, label='导弹轨迹')
    else:
        ax.plot(missile_traj[:, 0], missile_traj[:, 1], missile_traj[:, 2], 
                color=COLORS['missile'], linewidth=3, alpha=0.8, label='Missile Trajectory')
    
    # 关键点标记
    if language == 'zh':
        ax.scatter(*P_M0, color=COLORS['missile'], s=120, marker='^', 
                  label='M1起点', edgecolors='white', linewidth=2, zorder=5)
        ax.scatter(*P_U0, color=COLORS['uav'], s=120, marker='s', 
                  label='FY1起点', edgecolors='white', linewidth=2, zorder=5)
        ax.scatter(*P_T_center, color=COLORS['target'], s=180, marker='*', 
                  label='目标中心', edgecolors='white', linewidth=2, zorder=5)
    else:
        ax.scatter(*P_M0, color=COLORS['missile'], s=120, marker='^', 
                  label='M1 Start', edgecolors='white', linewidth=2, zorder=5)
        ax.scatter(*P_U0, color=COLORS['uav'], s=120, marker='s', 
                  label='FY1 Start', edgecolors='white', linewidth=2, zorder=5)
        ax.scatter(*P_T_center, color=COLORS['target'], s=180, marker='*', 
                  label='Target Center', edgecolors='white', linewidth=2, zorder=5)
    
    # 目标圆柱体
    theta = np.linspace(0, 2*np.pi, 32)
    x_cyl = P_T_bottom[0] + R_T * np.cos(theta)
    y_cyl = P_T_bottom[1] + R_T * np.sin(theta)
    z_bottom = np.full_like(x_cyl, P_T_bottom[2])
    z_top = np.full_like(x_cyl, P_T_bottom[2] + H_T)
    
    ax.plot(x_cyl, y_cyl, z_bottom, color=COLORS['target'], alpha=0.8, linewidth=2)
    ax.plot(x_cyl, y_cyl, z_top, color=COLORS['target'], alpha=0.8, linewidth=2)
    
    # 数据标注
    if language == 'zh':
        ax.text2D(0.02, 0.98, f'投放点: ({P_U0[0]:.0f}, {P_U0[1]:.0f}, {P_U0[2]:.0f})', 
                  transform=ax.transAxes, fontsize=11, verticalalignment='top',
                  bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
        ax.text2D(0.02, 0.92, f'起爆时间: t = {t_det:.1f}秒', 
                  transform=ax.transAxes, fontsize=11, verticalalignment='top',
                  bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
    else:
        ax.text2D(0.02, 0.98, f'Drop Point: ({P_U0[0]:.0f}, {P_U0[1]:.0f}, {P_U0[2]:.0f})', 
                  transform=ax.transAxes, fontsize=11, verticalalignment='top',
                  bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
        ax.text2D(0.02, 0.92, f'Detonation: t = {t_det:.1f}s', 
                  transform=ax.transAxes, fontsize=11, verticalalignment='top',
                  bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
    
    ax.set_xlabel('X (m)', fontsize=13, weight='bold')
    ax.set_ylabel('Y (m)', fontsize=13, weight='bold')
    ax.set_zlabel('Z (m)', fontsize=13, weight='bold')
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10, framealpha=0.9)
    ax.view_init(elev=20, azim=45)
    ax.grid(True, alpha=0.3)

def plot_accurate_blocking_analysis(ax, language='en'):
    """精确遮蔽效果分析"""
    # 高精度时间采样
    t_range = np.linspace(t_det, t_det + T_eff, 2000)
    blocking_status = [is_blocked(t) for t in t_range]
    
    # 绘制遮蔽状态
    if language == 'zh':
        ax.fill_between(t_range, 0, blocking_status, alpha=0.6, color=COLORS['blocking'], 
                        label='有效遮蔽时段')
    else:
        ax.fill_between(t_range, 0, blocking_status, alpha=0.6, color=COLORS['blocking'], 
                        label='Effective Blocking Period')
    ax.plot(t_range, blocking_status, color='darkgreen', linewidth=2.5)
    
    # 精确数值计算
    dt = t_range[1] - t_range[0]
    total_time = sum(blocking_status) * dt
    efficiency = total_time / T_eff * 100
    
    # 数值标注
    if language == 'zh':
        textstr = f'总遮蔽时间: {total_time:.3f}秒\n遮蔽效率: {efficiency:.2f}%\n窗口时长: {T_eff:.1f}秒'
    else:
        textstr = f'Total Blocking Time: {total_time:.3f}s\nBlocking Efficiency: {efficiency:.2f}%\nWindow Duration: {T_eff:.1f}s'
    
    props = dict(boxstyle='round', facecolor='wheat', alpha=0.9, edgecolor='orange')
    ax.text(0.05, 0.95, textstr, transform=ax.transAxes, fontsize=12,
            verticalalignment='top', bbox=props, weight='bold')
    
    # 时间标记
    if language == 'zh':
        ax.axvline(x=t_det, color=COLORS['missile'], linestyle='--', alpha=0.8, 
                   linewidth=2, label='起爆开始')
        ax.axvline(x=t_det + T_eff, color=COLORS['missile'], linestyle='--', alpha=0.8, 
                   linewidth=2, label='效果失效')
        ax.set_xlabel('时间 (秒)', fontsize=13, weight='bold')
        ax.set_ylabel('遮蔽状态', fontsize=13, weight='bold')
    else:
        ax.axvline(x=t_det, color=COLORS['missile'], linestyle='--', alpha=0.8, 
                   linewidth=2, label='Detonation Start')
        ax.axvline(x=t_det + T_eff, color=COLORS['missile'], linestyle='--', alpha=0.8, 
                   linewidth=2, label='Effect Expiration')
        ax.set_xlabel('Time (s)', fontsize=13, weight='bold')
        ax.set_ylabel('Blocking Status', fontsize=13, weight='bold')
    
    ax.set_ylim(-0.05, 1.05)
    ax.legend(fontsize=11, framealpha=0.9)
    ax.grid(True, alpha=0.4)

def plot_enhanced_parameters(ax, language='en'):
    """美化版参数演化图"""
    t_range = np.linspace(t_det, t_det + T_eff, 200)
    
    d_axis_vals = []
    r_cone_vals = []
    sum_vals = []
    
    # 计算真实几何参数
    for t in t_range:
        V = P_M(t)
        A = view_cone_axis(t)
        theta = view_cone_half_angle(t)
        C = P_C(t)
        
        L = sub(C, V)
        d_along = dot(L, A)
        
        if d_along > 0:
            L2 = dot(L, L)
            perp2 = max(L2 - d_along * d_along, 0.0)
            d_axis = sqrt(perp2)
            R_cone = d_along * tan(theta)
            sum_val = d_axis + R_cone
        else:
            d_axis = np.nan
            R_cone = 0
            sum_val = np.nan
            
        d_axis_vals.append(d_axis)
        r_cone_vals.append(R_cone)
        sum_vals.append(sum_val)
    
    # 绘制参数曲线
    ax.plot(t_range, d_axis_vals, color='#3498DB', linewidth=3, label='$d_{axis}(t)$')
    ax.plot(t_range, r_cone_vals, color='#E74C3C', linewidth=3, label='$R_{cone}(t)$')
    ax.plot(t_range, sum_vals, color='#9B59B6', linewidth=3, label='$d_{axis} + R_{cone}$')
    
    if language == 'zh':
        ax.axhline(y=R_C, color=COLORS['smoke'], linestyle='--', linewidth=3, 
                   label=f'$R_C$ = {R_C}m (烟幕半径)')
    else:
        ax.axhline(y=R_C, color=COLORS['smoke'], linestyle='--', linewidth=3, 
                   label=f'$R_C$ = {R_C}m (Smoke Radius)')
    
    # 遮蔽条件区域
    valid_mask = ~np.isnan(sum_vals)
    blocked_mask = np.array(sum_vals) <= R_C
    combined_mask = valid_mask & blocked_mask
    
    if np.any(combined_mask):
        if language == 'zh':
            ax.fill_between(t_range, 0, 15, where=combined_mask, alpha=0.2, 
                            color=COLORS['blocking'], label='遮蔽条件满足')
        else:
            ax.fill_between(t_range, 0, 15, where=combined_mask, alpha=0.2, 
                            color=COLORS['blocking'], label='Blocking Condition Satisfied')
    
    if language == 'zh':
        ax.set_xlabel('时间 (秒)', fontsize=13, weight='bold')
        ax.set_ylabel('距离 (米)', fontsize=13, weight='bold')
    else:
        ax.set_xlabel('Time (s)', fontsize=13, weight='bold')
        ax.set_ylabel('Distance (m)', fontsize=13, weight='bold')
    
    ax.legend(fontsize=11, framealpha=0.9)
    ax.grid(True, alpha=0.4)
    ax.set_ylim(0, 15)

def export_enhanced_individual_figures():
    """导出美化版单独图片"""
    
    fig_size = (10, 8)  # 更大的尺寸
    
    # 1. 几何模型图 - 中文版
    set_chinese_font()
    fig1_zh = plt.figure(figsize=fig_size, facecolor='white')
    ax1_zh = fig1_zh.add_subplot(111)
    plot_enhanced_geometric_model(ax1_zh, 'zh')
    plt.tight_layout()
    fig1_zh.savefig('enhanced_figure1_geometric_model_zh.png', dpi=300, bbox_inches='tight', facecolor='white')
    fig1_zh.savefig('enhanced_figure1_geometric_model_zh.pdf', bbox_inches='tight', facecolor='white')
    plt.close(fig1_zh)
    
    # 1. 几何模型图 - 英文版
    set_english_font()
    fig1_en = plt.figure(figsize=fig_size, facecolor='white')
    ax1_en = fig1_en.add_subplot(111)
    plot_enhanced_geometric_model(ax1_en, 'en')
    plt.tight_layout()
    fig1_en.savefig('enhanced_figure1_geometric_model_en.png', dpi=300, bbox_inches='tight', facecolor='white')
    fig1_en.savefig('enhanced_figure1_geometric_model_en.pdf', bbox_inches='tight', facecolor='white')
    plt.close(fig1_en)
    
    # 2. 3D轨迹图 - 中文版
    set_chinese_font()
    fig2_zh = plt.figure(figsize=fig_size, facecolor='white')
    ax2_zh = fig2_zh.add_subplot(111, projection='3d')
    plot_enhanced_3d_trajectories(ax2_zh, 'zh')
    plt.tight_layout()
    fig2_zh.savefig('enhanced_figure2_3d_trajectories_zh.png', dpi=300, bbox_inches='tight', facecolor='white')
    fig2_zh.savefig('enhanced_figure2_3d_trajectories_zh.pdf', bbox_inches='tight', facecolor='white')
    plt.close(fig2_zh)
    
    # 2. 3D轨迹图 - 英文版
    set_english_font()
    fig2_en = plt.figure(figsize=fig_size, facecolor='white')
    ax2_en = fig2_en.add_subplot(111, projection='3d')
    plot_enhanced_3d_trajectories(ax2_en, 'en')
    plt.tight_layout()
    fig2_en.savefig('enhanced_figure2_3d_trajectories_en.png', dpi=300, bbox_inches='tight', facecolor='white')
    fig2_en.savefig('enhanced_figure2_3d_trajectories_en.pdf', bbox_inches='tight', facecolor='white')
    plt.close(fig2_en)
    
    # 3. 遮蔽效果图 - 中文版
    set_chinese_font()
    fig3_zh = plt.figure(figsize=fig_size, facecolor='white')
    ax3_zh = fig3_zh.add_subplot(111)
    plot_accurate_blocking_analysis(ax3_zh, 'zh')
    plt.tight_layout()
    fig3_zh.savefig('enhanced_figure3_blocking_analysis_zh.png', dpi=300, bbox_inches='tight', facecolor='white')
    fig3_zh.savefig('enhanced_figure3_blocking_analysis_zh.pdf', bbox_inches='tight', facecolor='white')
    plt.close(fig3_zh)
    
    # 3. 遮蔽效果图 - 英文版
    set_english_font()
    fig3_en = plt.figure(figsize=fig_size, facecolor='white')
    ax3_en = fig3_en.add_subplot(111)
    plot_accurate_blocking_analysis(ax3_en, 'en')
    plt.tight_layout()
    fig3_en.savefig('enhanced_figure3_blocking_analysis_en.png', dpi=300, bbox_inches='tight', facecolor='white')
    fig3_en.savefig('enhanced_figure3_blocking_analysis_en.pdf', bbox_inches='tight', facecolor='white')
    plt.close(fig3_en)
    
    # 4. 参数演化图 - 中文版
    set_chinese_font()
    fig4_zh = plt.figure(figsize=fig_size, facecolor='white')
    ax4_zh = fig4_zh.add_subplot(111)
    plot_enhanced_parameters(ax4_zh, 'zh')
    plt.tight_layout()
    fig4_zh.savefig('enhanced_figure4_parameter_evolution_zh.png', dpi=300, bbox_inches='tight', facecolor='white')
    fig4_zh.savefig('enhanced_figure4_parameter_evolution_zh.pdf', bbox_inches='tight', facecolor='white')
    plt.close(fig4_zh)
    
    # 4. 参数演化图 - 英文版
    set_english_font()
    fig4_en = plt.figure(figsize=fig_size, facecolor='white')
    ax4_en = fig4_en.add_subplot(111)
    plot_enhanced_parameters(ax4_en, 'en')
    plt.tight_layout()
    fig4_en.savefig('enhanced_figure4_parameter_evolution_en.png', dpi=300, bbox_inches='tight', facecolor='white')
    fig4_en.savefig('enhanced_figure4_parameter_evolution_en.pdf', bbox_inches='tight', facecolor='white')
    plt.close(fig4_en)

if __name__ == "__main__":
    print("正在导出美化版单独图片...")
    export_enhanced_individual_figures()
    
    # 获取精确数据
    total_time = compute_effective遮蔽时长(0.001)
    
    print("\n" + "="*60)
    print("美化版图片导出完成 - 数据准确性验证")
    print("="*60)
    print(f"烟幕球半径: {R_C} 米")
    print(f"有效时间窗口: {T_eff} 秒")
    print(f"总有效遮蔽时长: {total_time:.4f} 秒")
    print(f"遮蔽效率: {total_time/T_eff*100:.2f}%")
    print("="*60)
    
    print("\n成功导出8张美化版图片:")
    print("中文版本:")
    print("1. enhanced_figure1_geometric_model_zh.png/pdf - 几何模型图")
    print("2. enhanced_figure2_3d_trajectories_zh.png/pdf - 3D轨迹图")
    print("3. enhanced_figure3_blocking_analysis_zh.png/pdf - 遮蔽效果图")
    print("4. enhanced_figure4_parameter_evolution_zh.png/pdf - 参数演化图")
    print("\n英文版本:")
    print("1. enhanced_figure1_geometric_model_en.png/pdf - Geometric Model")
    print("2. enhanced_figure2_3d_trajectories_en.png/pdf - 3D Trajectories")
    print("3. enhanced_figure3_blocking_analysis_en.png/pdf - Blocking Analysis")
    print("4. enhanced_figure4_parameter_evolution_en.png/pdf - Parameter Evolution")
    print("\n✨ 所有图片均为高分辨率(300 DPI)，数据与代码完全一致！")
