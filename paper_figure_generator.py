"""
论文专用图表生成器 - 问题1建立与求解部分配图
生成适合论文使用的高质量、简洁明了的示意图
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import seaborn as sns

# 设置论文图表样式
plt.rcParams['font.sans-serif'] = ['Times New Roman', 'SimHei']
plt.rcParams['font.size'] = 12
plt.rcParams['axes.linewidth'] = 1.2
plt.rcParams['lines.linewidth'] = 2
sns.set_palette("husl")

# 导入问题1的核心函数
from problem1 import *

def create_paper_figure():
    """创建适合论文的问题1综合分析图"""
    
    # 创建2x2布局的图形
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(14, 10))
    fig.suptitle('问题1：烟幕弹投放策略建立与求解', fontsize=16, fontweight='bold', y=0.95)
    
    # 子图1：几何模型示意图
    plot_geometric_model(ax1)
    
    # 子图2：3D轨迹与关键点
    ax2 = fig.add_subplot(2, 2, 2, projection='3d')
    plot_3d_scenario(ax2)
    
    # 子图3：遮蔽效果时间分析
    plot_blocking_analysis(ax3)
    
    # 子图4：关键参数变化
    plot_parameter_evolution(ax4)
    
    plt.tight_layout()
    return fig

def plot_geometric_model(ax):
    """绘制几何模型示意图 - 锥球相交原理"""
    ax.set_xlim(-2, 8)
    ax.set_ylim(-3, 3)
    
    # 绘制视锥
    cone_vertex = np.array([0, 0])
    cone_angle = 0.6  # 视锥半角
    cone_length = 6
    
    # 视锥边界
    upper_line = cone_vertex + cone_length * np.array([np.cos(cone_angle), np.sin(cone_angle)])
    lower_line = cone_vertex + cone_length * np.array([np.cos(-cone_angle), np.sin(-cone_angle)])
    
    ax.plot([cone_vertex[0], upper_line[0]], [cone_vertex[1], upper_line[1]], 'r-', linewidth=2, label='视锥边界')
    ax.plot([cone_vertex[0], lower_line[0]], [cone_vertex[1], lower_line[1]], 'r-', linewidth=2)
    ax.plot([cone_vertex[0], 6], [cone_vertex[1], 0], 'r--', linewidth=1, alpha=0.7, label='视锥轴线')
    
    # 绘制烟幕球
    smoke_center = np.array([4, 0.5])
    smoke_radius = 1.2
    circle = plt.Circle(smoke_center, smoke_radius, fill=False, color='orange', linewidth=3, label='烟幕球')
    ax.add_patch(circle)
    
    # 绘制目标
    target_pos = np.array([6, 0])
    ax.scatter(*target_pos, color='purple', s=200, marker='*', label='保护目标', zorder=5)
    
    # 标注关键几何量
    ax.annotate('导弹', cone_vertex, xytext=(-0.5, -0.5), fontsize=10, ha='center',
                arrowprops=dict(arrowstyle='->', color='red', lw=1))
    ax.annotate('$R_C$', smoke_center + np.array([0.8, 0.8]), fontsize=12, ha='center')
    ax.annotate('$d_{axis}$', [smoke_center[0], 0], xytext=[smoke_center[0], -1.5], 
                fontsize=12, ha='center', arrowprops=dict(arrowstyle='<->', color='blue', lw=1))
    
    # 绘制辅助线
    ax.plot([smoke_center[0], smoke_center[0]], [0, smoke_center[1]], 'b--', alpha=0.7)
    ax.plot([0, smoke_center[0]], [0, 0], 'b--', alpha=0.7)
    
    ax.set_xlabel('距离 (相对单位)')
    ax.set_ylabel('距离 (相对单位)')
    ax.set_title('(a) 锥-球相交几何模型')
    ax.legend(loc='upper left', fontsize=10)
    ax.grid(True, alpha=0.3)
    ax.set_aspect('equal')

def plot_3d_scenario(ax):
    """绘制3D场景概览"""
    # 关键时刻的位置
    t_key = t_det + 5  # 选择一个代表性时刻
    
    # 计算各实体位置
    pos_M = P_M(t_key)
    pos_C = P_C(t_key)
    pos_U = P_U(t_key) if t_key <= 15 else P_U(15)
    
    # 绘制轨迹
    t_range = np.linspace(0, 25, 100)
    missile_traj = np.array([P_M(t) for t in t_range])
    
    t_smoke = t_range[t_range >= t_det]
    smoke_traj = np.array([P_C(t) for t in t_smoke])
    
    # 绘制轨迹线
    ax.plot(missile_traj[:, 0], missile_traj[:, 1], missile_traj[:, 2], 
            'r-', linewidth=2, label='导弹轨迹', alpha=0.8)
    ax.plot(smoke_traj[:, 0], smoke_traj[:, 1], smoke_traj[:, 2], 
            'orange', linewidth=3, label='烟幕轨迹', alpha=0.9)
    
    # 标记关键点
    ax.scatter(*P_M0, color='red', s=100, marker='^', label='M1起点')
    ax.scatter(*P_U0, color='blue', s=100, marker='s', label='FY1起点')
    ax.scatter(*P_T_center, color='purple', s=150, marker='*', label='目标')
    ax.scatter(*pos_C, color='orange', s=200, marker='o', label=f'烟幕球(t={t_key:.1f}s)')
    
    # 绘制目标圆柱体（简化）
    theta = np.linspace(0, 2*np.pi, 20)
    x_cyl = P_T_bottom[0] + R_T * np.cos(theta)
    y_cyl = P_T_bottom[1] + R_T * np.sin(theta)
    z_bottom = np.full_like(x_cyl, P_T_bottom[2])
    z_top = np.full_like(x_cyl, P_T_bottom[2] + H_T)
    
    ax.plot(x_cyl, y_cyl, z_bottom, 'purple', alpha=0.6)
    ax.plot(x_cyl, y_cyl, z_top, 'purple', alpha=0.6)
    
    ax.set_xlabel('X (m)')
    ax.set_ylabel('Y (m)')
    ax.set_zlabel('Z (m)')
    ax.set_title('(b) 三维场景示意图')
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=9)
    
    # 设置视角
    ax.view_init(elev=20, azim=45)

def plot_blocking_analysis(ax):
    """绘制遮蔽效果分析"""
    # 时间范围
    t_range = np.linspace(t_det, t_det + T_eff, 1000)
    blocking_status = [is_blocked(t) for t in t_range]
    
    # 绘制遮蔽状态
    ax.fill_between(t_range, 0, blocking_status, alpha=0.6, color='lightgreen', 
                    label='有效遮蔽时段')
    ax.plot(t_range, blocking_status, 'darkgreen', linewidth=2, alpha=0.8)
    
    # 计算并标注关键数值
    total_time = sum(blocking_status) * (t_range[1] - t_range[0])
    efficiency = total_time / T_eff * 100
    
    # 添加文本框
    textstr = f'总遮蔽时长: {total_time:.2f}s\n遮蔽效率: {efficiency:.1f}%'
    props = dict(boxstyle='round', facecolor='wheat', alpha=0.8)
    ax.text(0.05, 0.95, textstr, transform=ax.transAxes, fontsize=11,
            verticalalignment='top', bbox=props)
    
    # 标记关键时间点
    ax.axvline(x=t_det, color='red', linestyle='--', alpha=0.7, label='起爆时刻')
    ax.axvline(x=t_det + T_eff, color='red', linestyle='--', alpha=0.7, label='失效时刻')
    
    ax.set_xlabel('时间 (s)')
    ax.set_ylabel('遮蔽状态')
    ax.set_title('(c) 遮蔽效果时间分析')
    ax.set_ylim(-0.05, 1.05)
    ax.legend(fontsize=10)
    ax.grid(True, alpha=0.3)

def plot_parameter_evolution(ax):
    """绘制关键参数演化"""
    t_range = np.linspace(t_det, t_det + T_eff, 200)
    
    d_axis_values = []
    r_cone_values = []
    sum_values = []
    
    for t in t_range:
        V = P_M(t)
        A = view_cone_axis(t)
        theta = view_cone_half_angle(t)
        C = P_C(t)
        
        L = sub(C, V)
        d_along = dot(L, A)
        
        if d_along > 0:
            L2 = dot(L, L)
            perp2 = max(L2 - d_along * d_along, 0.0)
            d_axis = sqrt(perp2)
            R_cone = d_along * tan(theta)
        else:
            d_axis = np.nan
            R_cone = 0
            
        d_axis_values.append(d_axis)
        r_cone_values.append(R_cone)
        sum_values.append(d_axis + R_cone if not np.isnan(d_axis) else np.nan)
    
    # 绘制参数曲线
    ax.plot(t_range, d_axis_values, 'b-', linewidth=2, label='$d_{axis}(t)$')
    ax.plot(t_range, r_cone_values, 'r-', linewidth=2, label='$R_{cone}(t)$')
    ax.plot(t_range, sum_values, 'purple', linewidth=2, label='$d_{axis}+R_{cone}$')
    ax.axhline(y=R_C, color='orange', linestyle='--', linewidth=2, label='$R_C$ (烟幕半径)')
    
    # 填充遮蔽区域
    valid_mask = ~np.isnan(sum_values)
    blocked_mask = np.array(sum_values) <= R_C
    combined_mask = valid_mask & blocked_mask
    
    if np.any(combined_mask):
        ax.fill_between(t_range, 0, 15, where=combined_mask, alpha=0.2, color='green', 
                        label='遮蔽条件满足')
    
    ax.set_xlabel('时间 (s)')
    ax.set_ylabel('距离 (m)')
    ax.set_title('(d) 关键几何参数演化')
    ax.legend(fontsize=10)
    ax.grid(True, alpha=0.3)
    ax.set_ylim(0, 15)

def create_model_diagram():
    """创建模型建立流程图"""
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))
    
    # 流程框
    boxes = [
        {'pos': (2, 7), 'text': '建立坐标系\n定义参数', 'color': 'lightblue'},
        {'pos': (6, 7), 'text': '实体运动学\n建模', 'color': 'lightgreen'},
        {'pos': (10, 7), 'text': '几何遮蔽\n模型', 'color': 'lightyellow'},
        {'pos': (4, 4), 'text': '视锥构建', 'color': 'lightcoral'},
        {'pos': (8, 4), 'text': '锥-球相交\n判定', 'color': 'lightpink'},
        {'pos': (6, 1), 'text': '遮蔽时长\n计算', 'color': 'lightgray'},
    ]
    
    # 绘制流程框
    for box in boxes:
        rect = FancyBboxPatch((box['pos'][0]-0.8, box['pos'][1]-0.5), 1.6, 1, 
                             boxstyle="round,pad=0.1", facecolor=box['color'], 
                             edgecolor='black', linewidth=1.5)
        ax.add_patch(rect)
        ax.text(box['pos'][0], box['pos'][1], box['text'], ha='center', va='center', 
                fontsize=11, fontweight='bold')
    
    # 绘制箭头
    arrows = [
        ((2.8, 7), (5.2, 7)),
        ((6.8, 7), (9.2, 7)),
        ((6, 6.5), (4.5, 4.5)),
        ((10, 6.5), (8.5, 4.5)),
        ((4.5, 3.5), (6.5, 1.5)),
        ((7.5, 3.5), (6.5, 1.5)),
    ]
    
    for start, end in arrows:
        ax.annotate('', xy=end, xytext=start,
                   arrowprops=dict(arrowstyle='->', lw=2, color='darkblue'))
    
    ax.set_xlim(0, 12)
    ax.set_ylim(0, 8)
    ax.set_title('问题1模型建立流程图', fontsize=16, fontweight='bold')
    ax.axis('off')
    
    return fig

if __name__ == "__main__":
    # 生成主要分析图
    print("正在生成问题1分析图...")
    fig1 = create_paper_figure()
    fig1.savefig('problem1_paper_figure.png', dpi=300, bbox_inches='tight')
    fig1.savefig('problem1_paper_figure.pdf', bbox_inches='tight')
    
    # 生成模型流程图
    print("正在生成模型建立流程图...")
    fig2 = create_model_diagram()
    fig2.savefig('problem1_model_flow.png', dpi=300, bbox_inches='tight')
    fig2.savefig('problem1_model_flow.pdf', bbox_inches='tight')
    
    print("\n论文图表已生成：")
    print("1. problem1_paper_figure.png/pdf - 问题1综合分析图")
    print("2. problem1_model_flow.png/pdf - 模型建立流程图")
    
    plt.show()
