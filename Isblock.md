### **模型核心思想**

为实现对具有体积的目标（圆柱体）的有效遮蔽，烟幕必须阻挡从导弹射向目标**所有可见部分**的全部视线。这些视线在几何上构成一个以导弹为顶点、以目标轮廓为边界的**视锥 (Sight Cone)**。

因此，遮蔽问题转化为一个“**锥-球相交**”问题：在时刻 $t$，烟幕球体是否完全包含了该时刻动态变化的视锥？

### **第一步：定义系统内各关键要素的动态矢量**

在任意时刻 $t$，我们需要首先确定导弹、目标和烟幕球的几何状态。

#### **1. 目标（圆柱体）的几何参数 (静态)**

- 目标半径: $R_T = 7$ 米
- 目标高度: $H_T = 10$ 米
- 底面圆心: $\vec{C}_{bottom} = (0, 200, 0)$
- 顶面圆心: $\vec{C}_{top} = (0, 200, 10)$
- 几何中心: $\vec{P}_{T_{center}} = (0, 200, 5)$

#### **2. 导弹位置 (动态)**

- 导弹初始位置: $\vec{P}_{M0}$ (例如 M1 为 $(20000, 0, 2000)$)
- 导弹攻击目标点: $\vec{P}_{Tgt} = \vec{C}_{bottom} = (0, 200, 0)$
- 导弹速度大小: $v_M = 300$ 米/秒
- 导弹位置矢量 $\vec{P}_M(t)$:
  $$
  \vec{P}_M(t) = \vec{P}_{M0} + v_M \cdot t \cdot \frac{\vec{P}_{Tgt} - \vec{P}_{M0}}{\|\vec{P}_{Tgt} - \vec{P}_{M0}\|}
  $$

#### **3. 烟幕球 (动态)**

- 烟幕球半径: $R_C = 10$ 米
- 烟幕球心位置 $\vec{P}_C(t)$: 该位置取决于无人机(UAV)的飞行、投弹时刻 $t_{drop}$、起爆延迟 $t_{delay}$ 和下沉速度 $v_{descent} = 3$ 米/秒。
  1.  计算 UAV 在 $t_{drop}$ 刻的投弹点位置 $\vec{P}_{drop}$。
  2.  烟幕弹在空中自由下落或按预定轨迹运动，在 $t_{det} = t_{drop} + t_{delay}$ 时刻到达起爆点 $\vec{P}_{det}$。
  3.  对于 $t > t_{det}$，烟幕球心开始从 $\vec{P}_{det}$ 匀速下沉，其位置为：
      $$
      \vec{P}_C(t) = \vec{P}_{det} - (0, 0, v_{descent} \cdot (t - t_{det}))
      $$

---

### **第二步：精确构建动态视锥 (Sight Cone)**

视锥由其顶点、中轴线和半角唯一确定，这三个参数都随时间 $t$ 动态变化。

#### **A. 视锥顶点 $\vec{V}(t)$**

视锥的顶点就是导弹的位置：
$$\vec{V}(t) = \vec{P}_M(t)$$

#### **B. 视锥中轴线 $\vec{A}(t)$**

视锥的中轴线是从导弹指向目标几何中心的单位向量，这代表了导引头"注视"的方向：
$$\vec{A}(t) = \frac{\vec{P}_{T_{center}} - \vec{V}(t)}{\|\vec{P}_{T_{center}} - \vec{V}(t)\|}$$

#### **C. 视锥半角 $\theta(t)$**

这是模型的修正核心。为确保视锥能包裹整个圆柱体，其半角必须足以同时覆盖顶面圆和底面圆。

1.  **计算关键距离：**

    - 导弹到顶面圆心的距离: $d_{top}(t) = \|\vec{C}_{top} - \vec{V}(t)\|$
    - 导弹到底面圆心的距离: $d_{bottom}(t) = \|\vec{C}_{bottom} - \vec{V}(t)\|$

2.  **计算各圆盘的视角和偏角：**

    - 从导弹看，顶面圆半径所张开的半角:
      $$
      \theta_{top\_subtend}(t) = \arcsin\left(\min\left(\frac{R_T}{d_{top}(t)}, 1\right)\right)
      $$
    - 从导弹看，底面圆半径所张开的半角:
      $$
      \theta_{bottom\_subtend}(t) = \arcsin\left(\min\left(\frac{R_T}{d_{bottom}(t)}, 1\right)\right)
      $$
    - 视锥中轴线与"导弹-顶面圆心"连线的夹角:
      $$
      \alpha_{top}(t) = \arccos\left(\vec{A}(t) \cdot \frac{\vec{C}_{top} - \vec{V}(t)}{d_{top}(t)}\right)
      $$
    - 视锥中轴线与"导弹-底面圆心"连线的夹角:
      $$
      \alpha_{bottom}(t) = \arccos\left(\vec{A}(t) \cdot \frac{\vec{C}_{bottom} - \vec{V}(t)}{d_{bottom}(t)}\right)
      $$

3.  **确定最终的视锥半角：**
    视锥半角 $\theta(t)$ 必须取"覆盖顶面所需总角度"和"覆盖底面所需总角度"中的较大者。
    $$
    \theta(t) = \max\left( \alpha_{top}(t) + \theta_{top\_subtend}(t), \quad \alpha_{bottom}(t) + \theta_{bottom\_subtend}(t) \right)
    $$

---

### **第三步：最终遮蔽判据**

有了定义精确的视锥（顶点$\vec{V}(t)$、轴线$\vec{A}(t)$、半角$\theta(t)$）和烟幕球（球心$\vec{P}_C(t)$、半径$R_C$），我们可以通过一个严谨的几何判据来判断是否实现完全遮蔽。

1.  **定义关键向量：**

    - 从视锥顶点（导弹）指向烟幕球心的向量: $\vec{L}(t) = \vec{P}_C(t) - \vec{V}(t)$

2.  **计算投影与垂直距离：**

    - 将烟幕球心在视锥中轴线上的投影长度:
      $$
      d_{along}(t) = \vec{L}(t) \cdot \vec{A}(t)
      $$
    - 烟幕球心到视锥中轴线的垂直距离:
      使用向量叉乘：
      $$
      d_{axis}(t) = \|\vec{L}(t) \times \vec{A}(t)\|
      $$
      原来的公式为：
      $$
      d_{axis}(t) = \sqrt{\|\vec{L}(t)\|^2 - d_{along}(t)^2}
      $$

3.  **计算在该投影位置的视锥半径：**

    - 在距离顶点 $d_{along}(t)$ 处，视锥的横截面半径为:
      $$
      R_{cone}(t) = d_{along}(t) \cdot \tan(\theta(t))
      $$

4.  **最终遮蔽条件：**
    当且仅当烟幕球的半径 $R_C$ 足以跨过其自身到轴线的偏移距离 $d_{axis}(t)$ 并覆盖掉该处的视锥半径 $R_{cone}(t)$ 时，遮蔽才成立。

    **目标在时刻 t 被有效遮蔽的充要条件是：**

    $$
    d_{axis}(t) + R_{cone}(t) \le R_C
    $$
